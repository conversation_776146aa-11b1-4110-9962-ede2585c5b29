<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Get all clients for the current user
$stmt = $pdo->prepare("SELECT * FROM clients WHERE user_id = ? ORDER BY name");
$stmt->execute([$_SESSION['user_id']]);
$clients = $stmt->fetchAll();

// Get all items for the current user
$stmt = $pdo->prepare("SELECT * FROM items WHERE user_id = ? ORDER BY name");
$stmt->execute([$_SESSION['user_id']]);
$items = $stmt->fetchAll();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();

        // Insert invoice
        $stmt = $pdo->prepare("INSERT INTO invoices (user_id, client_id, invoice_number, issue_date, due_date, status, subtotal, tax_rate, tax_amount, total, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $_SESSION['user_id'],
            $_POST['client_id'],
            $_POST['invoice_number'],
            $_POST['issue_date'],
            $_POST['due_date'],
            $_POST['status'],
            $_POST['subtotal'],
            $_POST['tax_rate'],
            $_POST['tax_amount'],
            $_POST['total'],
            $_POST['notes']
        ]);
        $invoice_id = $pdo->lastInsertId();

        // Insert invoice items
        $stmt = $pdo->prepare("INSERT INTO invoice_items (invoice_id, item_id, description, quantity, unit_price, amount) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($_POST['items'] as $item) {
            $stmt->execute([
                $invoice_id,
                $item['item_id'],
                $item['description'],
                $item['quantity'],
                $item['unit_price'],
                $item['amount']
            ]);
        }

        $pdo->commit();
        header('Location: view_invoice.php?id=' . $invoice_id);
        exit;
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "Error creating invoice: " . $e->getMessage();
    }
}

$page_title = "Create New Invoice";
include 'includes/header.php';
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-file-earmark-plus"></i> Create New Invoice</h2>
        </div>
    </div>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
    </div>
    <?php endif; ?>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="post" id="invoice-form">
                <!-- Client and Dates Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-person"></i> Client & Dates</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">Client</label>
                                <select name="client_id" class="form-select" required>
                                    <option value="">Select Client</option>
                                    <?php foreach($clients as $client): ?>
                                    <option value="<?= $client['id'] ?>"><?= htmlspecialchars($client['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Quotation Date</label>
                                <input type="date" name="quote_date" class="form-control" id="quote_date" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Invoice Date</label>
                                <input type="date" name="invoice_date" class="form-control" id="invoice_date">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Paid Date</label>
                                <input type="date" name="paid_date" class="form-control" id="paid_date">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Details Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-info-circle"></i> Invoice Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-5">
                                <label class="form-label">Invoice Title</label>
                                <input type="text" name="invoice_for" class="form-control" placeholder="Enter invoice title">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Service Type</label>
                                <input type="text" name="service_type" placeholder="Add New Service Type" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Or Existing Type</label>
                                <select id="service_type" class="form-select" name="service_type">
                                    <!-- options will be populated here -->
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing & Discounts Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-currency-dollar"></i> Pricing & Discounts</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">Discount (%)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="discount_rate" class="form-control" value="0.00" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Discount (Fixed)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="discount_fixed" class="form-control" value="0.00" required>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">VAT Rate (%)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="vat_rate" class="form-control" value="5.00" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">TAX Exempt?</label>
                                <select name="tax_exempt" class="form-select">
                                    <option value="yes" selected>Yes</option>
                                    <option value="no">No</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Payment Status</label>
                                <select name="payment_status" class="form-select">
                                    <option value="unpaid" selected>Unpaid</option>
                                    <option value="processing">Processing</option>
                                    <option value="paid">Paid</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Discount Notes</label>
                                <input type="text" name="discount_notes" class="form-control" placeholder="Write discount notes">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-check"></i> Invoice Items</h5>
                        <button type="button" class="btn btn-sm btn-primary" id="add-row">
                            <i class="bi bi-plus-circle"></i> Add Item
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-no-border mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="35%">Item</th>
                                        <th width="15%">Price Override</th>
                                        <th width="30%">Special Notes</th>
                                        <th width="10%">Quantity</th>
                                        <th width="10%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="items-container">
                                    <tr class="item-row">
                                        <td>
                                            <select name="item_id[]" class="form-select item-select">
                                                <option value="">Select Item</option>
                                                <?php foreach($items as $item): ?>
                                                <option data-price="<?= $item['price'] ?>" value="<?= $item['id'] ?>">
                                                    <?= htmlspecialchars($item['name']) ?> (BDT <?= number_format($item['price'], 2) ?>)
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input type="number" name="price_override[]" class="form-control" placeholder="Override" value="">
                                            </div>
                                        </td>
                                        <td>
                                            <input type="text" name="special_notes[]" class="form-control" placeholder="Special Notes" value="">
                                        </td>
                                        <td>
                                            <input type="number" name="quantity[]" class="form-control quantity" placeholder="Qty" min="1" value="1">
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger remove-row">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Invoice Summary Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-calculator"></i> Invoice Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <div class="table-responsive">
                                    <table class="table table-sm table-borderless">
                                        <tbody>
                                            <tr>
                                                <td class="text-muted">Subtotal:</td>
                                                <td class="text-end fw-medium" id="subtotal">0.00</td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted" id="discount-label">Discount (0.00%):</td>
                                                <td class="text-end text-danger fw-medium" id="discount">-0.00</td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted">Total:</td>
                                                <td class="text-end fw-medium" id="after-discount">0.00</td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted" id="vat-label">VAT (5.00%):</td>
                                                <td class="text-end fw-medium" id="vat">0.00</td>
                                            </tr>
                                            <tr class="border-top pt-2">
                                                <td class="fw-bold">Grand Total:</td>
                                                <td class="text-end fw-bold fs-5" id="total-cell">0.00</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="invoices.php" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> Cancel
                    </a>
                    <button type="submit" name="create_invoice" class="btn btn-success btn-lg">
                        <i class="bi bi-check-circle"></i> Create Invoice
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('add-row').addEventListener('click', function() {
    const newRow = document.querySelector('.item-row').cloneNode(true);
    newRow.querySelectorAll('input').forEach(input => {
        if (input.classList.contains('quantity')) {
            input.value = '1';
        } else {
            input.value = '';
        }
    });
    newRow.querySelector('select').selectedIndex = 0;
    document.getElementById('items-container').appendChild(newRow);
});

// Fix the remove row functionality
document.addEventListener('click', function(e) {
    // Check if the click is on the button or any of its children (like the icon)
    const removeButton = e.target.closest('.remove-row');
    if (removeButton) {
        if (document.querySelectorAll('.item-row').length > 1) {
            removeButton.closest('.item-row').remove();
            // Recalculate totals after removing a row
            calculateTotals();
        } else {
            alert('You cannot remove the last item row.');
        }
    }
});

// Set default dates
const today = new Date();
const dueDate = new Date(today);
dueDate.setDate(dueDate.getDate() + 15);
document.getElementById('quote_date').value = new Date().toISOString().slice(0, 10);

// Calculate totals
function calculateTotals() {
    let subtotal = 0;
    
    document.querySelectorAll('.item-row').forEach(row => {
        const select = row.querySelector('.item-select');
        const quantity = parseInt(row.querySelector('.quantity').value) || 0;
        const priceOverride = parseFloat(row.querySelector('input[name="price_override[]"]').value) || 0;
        
        if (select.selectedIndex > 0) {
            const option = select.options[select.selectedIndex];
            const price = priceOverride > 0 ? priceOverride : parseFloat(option.dataset.price) || 0;
            subtotal += price * quantity;
        }
    });
    
    const discountRate = parseFloat(document.querySelector('input[name="discount_rate"]').value) || 0;
    const discountFixed = parseFloat(document.querySelector('input[name="discount_fixed"]').value) || 0;
    const vatRate = parseFloat(document.querySelector('input[name="vat_rate"]').value) || 0;
    const taxExempt = document.querySelector('select[name="tax_exempt"]').value;
    
    // Calculate discount amount
    let discountAmount = (subtotal * (discountRate / 100)) + discountFixed;
    const afterDiscount = subtotal - discountAmount;
    const vatAmount = afterDiscount * (vatRate / 100);
    const total = afterDiscount + vatAmount;
    
    // Format number with commas
    function formatNumber(num) {
        return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // Update discount label
    const discountLabel = document.getElementById('discount-label');
    if (discountLabel) {
        if (discountRate > 0 && discountFixed > 0) {
            discountLabel.textContent = `Discount (${discountRate.toFixed(2)}% + Fixed):`;
        } else if (discountRate > 0) {
            discountLabel.textContent = `Discount (${discountRate.toFixed(2)}%):`;
        } else if (discountFixed > 0) {
            discountLabel.textContent = 'Discount (Fixed):';
        } else {
            discountLabel.textContent = 'Discount:';
        }
    }
    
    // Update VAT label
    const vatLabel = document.getElementById('vat-label');
    if (vatLabel) {
        vatLabel.textContent = `VAT (${vatRate.toFixed(2)}%):`;
    }

    // Update all amounts with formatted numbers
    document.getElementById('subtotal').textContent = formatNumber(subtotal);
    document.getElementById('discount').textContent = '-' + formatNumber(discountAmount);
    document.getElementById('after-discount').textContent = formatNumber(afterDiscount);
    document.getElementById('vat').textContent = formatNumber(vatAmount);
    
    // Update total with tax exempt note if applicable
    const totalCell = document.getElementById('total-cell');
    if (totalCell) {
        if (taxExempt === 'yes') {
            totalCell.innerHTML = formatNumber(total) + '<small class="tax-exempt text-muted">(Tax Exempt)</small>';
        } else {
            totalCell.textContent = formatNumber(total);
        }
    }
}

// Add event listeners for calculation
document.querySelectorAll('input[name="discount_rate"], input[name="discount_fixed"], input[name="vat_rate"], select[name="tax_exempt"]')
    .forEach(el => el.addEventListener('input', calculateTotals));

document.getElementById('items-container').addEventListener('change', function(e) {
    if (e.target.classList.contains('item-select') || e.target.classList.contains('quantity') || 
        e.target.name === 'price_override[]') {
        calculateTotals();
    }
});

// Also listen for input events on quantity and price override
document.getElementById('items-container').addEventListener('input', function(e) {
    if (e.target.classList.contains('quantity') || e.target.name === 'price_override[]') {
        calculateTotals();
    }
});

// Initial calculation
calculateTotals();

// Service type handling
function fetchServiceTypes() {
    var dropdown = document.querySelector('select[name="service_type"]');
    if (dropdown !== null) {
        var currentValue = dropdown.value;

        var xhr = new XMLHttpRequest();
        xhr.open('GET', 'fetch_service_types.php', true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                var serviceTypes = JSON.parse(xhr.responseText);
                dropdown.innerHTML = '<option value="">Please Select</option>';
                serviceTypes.forEach(function(serviceType) {
                    if (serviceType.service_type !== null && serviceType.service_type !== '') {
                        var option = document.createElement('option');
                        option.value = serviceType.service_type;
                        option.text = serviceType.service_type;
                        if (currentValue === serviceType.service_type) {
                            option.selected = true;
                        }
                        dropdown.appendChild(option);
                    }
                });
            }
        };
        xhr.send();
    }
}

window.addEventListener('load', function() {
    fetchServiceTypes();
});

var inputField = document.querySelector('input[name="service_type"]');
var selectField = document.querySelector('select[name="service_type"]');

// Check initially if the input field is not empty
if (inputField.value !== '') {
    selectField.disabled = true;
}

inputField.addEventListener('input', function() {
    if (inputField.value !== '') {
        selectField.disabled = true;
    } else {
        selectField.disabled = false;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
