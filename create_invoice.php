<?php
require_once 'auth.php';
$page_title = "Klay Invoice - Create Invoice";
include 'includes/header.php'; 
include 'config.php';

if(isset($_POST['create_invoice'])){
    // Insert Invoice
    $stmt = $pdo->prepare("INSERT INTO invoices (client_id, invoice_for, service_type, invoice_date, quote_date, paid_date, discount_rate, discount_fixed, discount_notes, vat_rate, tax_exempt, payment_status) 
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    // Handle empty invoice_date by using NULL
    $invoice_date = !empty($_POST['invoice_date']) ? $_POST['invoice_date'] : null;
    
    $stmt->execute([
        $_POST['client_id'],
        $_POST['invoice_for'],
        $_POST['service_type'],
        $invoice_date,
        $_POST['quote_date'],
        !empty($_POST['paid_date']) ? $_POST['paid_date'] : null,
        $_POST['discount_rate'],
        $_POST['discount_fixed'],
        $_POST['discount_notes'],
        $_POST['vat_rate'],
        $_POST['tax_exempt'],
        $_POST['payment_status']
    ]);
    $invoice_id = $pdo->lastInsertId();

    // Insert Invoice Items
    foreach($_POST['item_id'] as $key => $item_id){
        if(!empty($item_id)){
            $item = $pdo->query("SELECT price FROM items WHERE id = $item_id")->fetch();

            $unit_price = !empty($_POST['price_override'][$key]) ? $_POST['price_override'][$key] : $item['price'];
            
            $stmt = $pdo->prepare("INSERT INTO invoice_items 
                (invoice_id, item_id, special_notes, quantity, unit_price) 
                VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                $invoice_id,
                $item_id,
                $_POST['special_notes'][$key],
                $_POST['quantity'][$key],
                $unit_price
            ]);
        }
    }
    header("Location: view_invoice.php?id=$invoice_id");
}

$clients = $pdo->query("SELECT * FROM clients ORDER BY name ASC")->fetchAll();
$items = $pdo->query("SELECT * FROM items ORDER BY name ASC")->fetchAll();
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-file-earmark-plus"></i> Create New Invoice</h2>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="post" id="invoice-form">
                <!-- Client and Dates Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-person"></i> Client & Dates</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">Client</label>
                                <select name="client_id" class="form-select form-control" required>
                                    <option value="">Select Client</option>
                                    <?php foreach($clients as $client): ?>
                                    <option value="<?= $client['id'] ?>"><?= htmlspecialchars($client['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Quotation Date</label>
                                <input type="date" name="quote_date" class="form-control" id="quote_date" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Invoice Date</label>
                                <input type="date" name="invoice_date" class="form-control" id="invoice_date">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Paid Date</label>
                                <input type="date" name="paid_date" class="form-control" id="paid_date">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Details Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-info-circle"></i> Invoice Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-5">
                                <label class="form-label">Invoice Title</label>
                                <input type="text" name="invoice_for" class="form-control" placeholder="Enter invoice title">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Service Type</label>
                                <input type="text" name="service_type" placeholder="Add New Service Type" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Or Existing Type</label>
                                <select id="service_type" class="form-select form-control" name="service_type">
                                    <!-- options will be populated here -->
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing & Discounts Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-currency-dollar"></i> Pricing & Discounts</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">Discount (%)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="discount_rate" class="form-control" value="0.00" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Discount (Fixed)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="discount_fixed" class="form-control" value="0.00" required>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">VAT Rate (%)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="vat_rate" class="form-control" value="5.00" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">TAX Exempt?</label>
                                <select name="tax_exempt" class="form-select form-control">
                                    <option value="yes" selected>Yes</option>
                                    <option value="no">No</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Payment Status</label>
                                <select name="payment_status" class="form-select form-control">
                                    <option value="unpaid" selected>Unpaid</option>
                                    <option value="processing">Processing</option>
                                    <option value="paid">Paid</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Discount Notes</label>
                                <input type="text" name="discount_notes" class="form-control" placeholder="Write discount notes">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-check"></i> Invoice Items</h5>
                        <button type="button" class="btn btn-sm btn-primary" id="add-row">
                            <i class="bi bi-plus-circle"></i> Add Item
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="35%">Item</th>
                                        <th width="15%">Price Override</th>
                                        <th width="30%">Special Notes</th>
                                        <th width="10%">Quantity</th>
                                        <th width="10%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="items-container">
                                    <tr class="item-row">
                                        <td>
                                            <select name="item_id[]" class="form-select form-control item-select">
                                                <option value="">Select Item</option>
                                                <?php foreach($items as $item): ?>
                                                <option data-price="<?= $item['price'] ?>" value="<?= $item['id'] ?>">
                                                    <?= htmlspecialchars($item['name']) ?> (BDT <?= number_format($item['price'], 2) ?>)
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input type="number" name="price_override[]" class="form-control" placeholder="Override" value="">
                                            </div>
                                        </td>
                                        <td>
                                            <input type="text" name="special_notes[]" class="form-control" placeholder="Special Notes" value="">
                                        </td>
                                        <td>
                                            <input type="number" name="quantity[]" class="form-control quantity" placeholder="Qty" min="1" value="1">
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger remove-row">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Invoice Summary Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-calculator"></i> Invoice Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td>Subtotal:</td>
                                        <td class="text-end" id="subtotal">0.00</td>
                                    </tr>
                                    <tr>
                                        <td id="discount-label">Discount (0.00%):</td>
                                        <td class="text-end" id="discount">-0.00</td>
                                    </tr>
                                    <tr>
                                        <td>Total:</td>
                                        <td class="text-end" id="after-discount">0.00</td>
                                    </tr>
                                    <tr>
                                        <td id="vat-label">VAT (5.00%):</td>
                                        <td class="text-end" id="vat">0.00</td>
                                    </tr>
                                    <tr class="table-active fw-bold">
                                        <td>Grand Total:</td>
                                        <td class="text-end" id="total-cell">0.00</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="invoices.php" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> Cancel
                    </a>
                    <button type="submit" name="create_invoice" class="btn btn-success btn-lg">
                        <i class="bi bi-check-circle"></i> Create Invoice
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('add-row').addEventListener('click', function() {
    const newRow = document.querySelector('.item-row').cloneNode(true);
    newRow.querySelectorAll('input').forEach(input => {
        if (input.classList.contains('quantity')) {
            input.value = '1';
        } else {
            input.value = '';
        }
    });
    newRow.querySelector('select').selectedIndex = 0;
    document.getElementById('items-container').appendChild(newRow);
});

// Fix the remove row functionality
document.addEventListener('click', function(e) {
    // Check if the click is on the button or any of its children (like the icon)
    const removeButton = e.target.closest('.remove-row');
    if (removeButton) {
        if (document.querySelectorAll('.item-row').length > 1) {
            removeButton.closest('.item-row').remove();
            // Recalculate totals after removing a row
            calculateTotals();
        } else {
            alert('You cannot remove the last item row.');
        }
    }
});

// Set default dates
const today = new Date();
const dueDate = new Date(today);
dueDate.setDate(dueDate.getDate() + 15);
//document.getElementById('invoice_date').value = dueDate.toISOString().slice(0, 10);
document.getElementById('quote_date').value = new Date().toISOString().slice(0, 10);

// Calculate totals
function calculateTotals() {
    let subtotal = 0;
    
    document.querySelectorAll('.item-row').forEach(row => {
        const select = row.querySelector('.item-select');
        const quantity = parseInt(row.querySelector('.quantity').value) || 0;
        const priceOverride = parseFloat(row.querySelector('input[name="price_override[]"]').value) || 0;
        
        if (select.selectedIndex > 0) {
            const option = select.options[select.selectedIndex];
            const price = priceOverride > 0 ? priceOverride : parseFloat(option.dataset.price) || 0;
            subtotal += price * quantity;
        }
    });
    
    const discountRate = parseFloat(document.querySelector('input[name="discount_rate"]').value) || 0;
    const discountFixed = parseFloat(document.querySelector('input[name="discount_fixed"]').value) || 0;
    const vatRate = parseFloat(document.querySelector('input[name="vat_rate"]').value) || 0;
    const taxExempt = document.querySelector('select[name="tax_exempt"]').value;
    
    // Calculate discount amount
    let discountAmount = (subtotal * (discountRate / 100)) + discountFixed;
    const afterDiscount = subtotal - discountAmount;
    const vatAmount = afterDiscount * (vatRate / 100);
    const total = afterDiscount + vatAmount;
    
    // Format number with commas
    function formatNumber(num) {
        return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // Update discount label
    const discountLabel = document.getElementById('discount-label');
    if (discountLabel) {
        if (discountRate > 0 && discountFixed > 0) {
            discountLabel.textContent = `Discount (${discountRate.toFixed(2)}% + Fixed):`;
        } else if (discountRate > 0) {
            discountLabel.textContent = `Discount (${discountRate.toFixed(2)}%):`;
        } else if (discountFixed > 0) {
            discountLabel.textContent = 'Discount (Fixed):';
        } else {
            discountLabel.textContent = 'Discount:';
        }
    }
    
    // Update VAT label
    const vatLabel = document.getElementById('vat-label');
    if (vatLabel) {
        vatLabel.textContent = `VAT (${vatRate.toFixed(2)}%):`;
    }
    
    // Update all amounts with formatted numbers
    document.getElementById('subtotal').textContent = formatNumber(subtotal);
    document.getElementById('discount').textContent = '-' + formatNumber(discountAmount);
    document.getElementById('after-discount').textContent = formatNumber(afterDiscount);
    document.getElementById('vat').textContent = formatNumber(vatAmount);
    
    // Update total with tax exempt note if applicable
    const totalCell = document.getElementById('total-cell');
    if (totalCell) {
        if (taxExempt === 'yes') {
            totalCell.innerHTML = formatNumber(total) + '<br><small class="text-muted">(Tax Exempt)</small>';
        } else {
            totalCell.textContent = formatNumber(total);
        }
    }
}

// Add event listeners for calculation
document.querySelectorAll('input[name="discount_rate"], input[name="discount_fixed"], input[name="vat_rate"], select[name="tax_exempt"]')
    .forEach(el => el.addEventListener('input', calculateTotals));

document.getElementById('items-container').addEventListener('change', function(e) {
    if (e.target.classList.contains('item-select') || e.target.classList.contains('quantity') || 
        e.target.name === 'price_override[]') {
        calculateTotals();
    }
});

// Also listen for input events on quantity and price override
document.getElementById('items-container').addEventListener('input', function(e) {
    if (e.target.classList.contains('quantity') || e.target.name === 'price_override[]') {
        calculateTotals();
    }
});

// Initial calculation
calculateTotals();

// Service type handling
function fetchServiceTypes() {
    var dropdown = document.getElementById('service_type');
    if (dropdown !== null) {
        var currentValue = dropdown.value;

        var xhr = new XMLHttpRequest();
        xhr.open('GET', 'fetch_service_types.php', true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                var serviceTypes = JSON.parse(xhr.responseText);
                dropdown.innerHTML = '<option value="">Please Select</option>';
                serviceTypes.forEach(function(serviceType) {
                    if (serviceType.service_type !== null && serviceType.service_type !== '') {
                        var option = document.createElement('option');
                        option.value = serviceType.service_type;
                        option.text = serviceType.service_type;
                        if (currentValue === serviceType.service_type) {
                            option.selected = true;
                        }
                        dropdown.appendChild(option);
                    }
                });
            }
        };
        xhr.send();
    }
}

window.addEventListener('load', function() {
    fetchServiceTypes();
});

var inputField = document.querySelector('input[name="service_type"]');
var selectField = document.querySelector('select[name="service_type"]');

// Check initially if the input field is not empty
if (inputField.value !== '') {
    selectField.disabled = true;
}

inputField.addEventListener('input', function() {
    if (inputField.value !== '') {
        selectField.disabled = true;
    } else {
        selectField.disabled = false;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
