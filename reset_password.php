<?php
// Start session
session_start();

// If already logged in, redirect to index
if(isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit();
}

// Include database connection
include 'config.php';

$message = '';
$message_type = '';
$show_form = false;
$token = '';


// Check if token is provided
if(isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];

    // First check if token exists at all
    $stmt = $pdo->prepare("SELECT prt.user_id, prt.expires_at, prt.created_at, u.username, u.email FROM password_reset_tokens prt JOIN users u ON prt.user_id = u.id WHERE prt.token = ?");
    $stmt->execute([$token]);
    $token_data = $stmt->fetch();

    if($token_data) {
        // Check if token is expired
        if(strtotime($token_data['expires_at']) > time()) {
            $show_form = true;
            $reset_data = $token_data;
        } else {
            $expired_minutes = round((time() - strtotime($token_data['expires_at'])) / 60);
            $message = "This password reset token has expired ($expired_minutes minutes ago). Please request a new password reset.";
            $message_type = 'warning';
        }
    } else {
        $message = "Invalid password reset token. Please request a new password reset.";
        $message_type = 'danger';
    }
} else {
    $message = "No password reset token provided.";
    $message_type = 'danger';
}

// Process password reset form
if(isset($_POST['reset_password']) && $show_form){
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validation
    if(empty($new_password) || empty($confirm_password)) {
        $message = "All fields are required";
        $message_type = "danger";
    } elseif($new_password !== $confirm_password) {
        $message = "Passwords do not match";
        $message_type = "danger";
    } elseif(strlen($new_password) < 6) {
        $message = "Password must be at least 6 characters long";
        $message_type = "danger";
    } else {
        // Update password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        
        if($stmt->execute([$hashed_password, $reset_data['user_id']])) {
            // Delete the used token
            $stmt = $pdo->prepare("DELETE FROM password_reset_tokens WHERE token = ?");
            $stmt->execute([$token]);
            
            $message = "Password reset successfully! You can now login with your new password.";
            $message_type = "success";
            $show_form = false;
        } else {
            $message = "Failed to reset password. Please try again.";
            $message_type = "danger";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Klay Invoice</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Custom Professional CSS -->
    <link rel="stylesheet" href="assets/css/professional.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }
        
        .reset-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            margin: 2rem;
        }
        
        .reset-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .reset-body {
            padding: 2rem;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .btn-action {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.875rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <div class="logo-icon">
                <?php if($message_type === 'success'): ?>
                    <i class="bi bi-check-circle fs-2"></i>
                <?php elseif($show_form): ?>
                    <i class="bi bi-shield-lock fs-2"></i>
                <?php else: ?>
                    <i class="bi bi-exclamation-triangle fs-2"></i>
                <?php endif; ?>
            </div>
            <h2 class="mb-1 fw-bold">Reset Password</h2>
            <p class="mb-0 opacity-75">Create a new password</p>
        </div>
        
        <div class="reset-body">
            <?php if(!empty($message)): ?>
                <div class="alert alert-<?= $message_type ?> border-0 rounded-3 mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <?php if($message_type === 'success'): ?>
                            <i class="bi bi-check-circle-fill me-2"></i>
                        <?php elseif($message_type === 'info'): ?>
                            <i class="bi bi-info-circle-fill me-2"></i>
                        <?php else: ?>
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php endif; ?>
                        <span><?= htmlspecialchars($message) ?></span>
                    </div>

                </div>
            <?php endif; ?>


            
            <?php if($show_form): ?>
                <p class="text-muted mb-4">
                    Enter your new password below. Make sure it's at least 6 characters long.
                </p>
                
                <form method="post">
                    <input type="hidden" name="token" value="<?= htmlspecialchars($token) ?>">
                    
                    <div class="form-floating mb-3">
                        <input type="password" class="form-control" id="new_password" name="new_password" placeholder="New Password" required>
                        <label for="new_password">
                            <i class="bi bi-lock me-2"></i>New Password
                        </label>
                    </div>

                    <div class="form-floating mb-4">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm Password" required>
                        <label for="confirm_password">
                            <i class="bi bi-lock-fill me-2"></i>Confirm Password
                        </label>
                    </div>

                    <div class="d-grid mb-3">
                        <button type="submit" name="reset_password" class="btn btn-primary btn-action btn-lg">
                            <i class="bi bi-check-circle me-2"></i>Reset Password
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <div class="text-center">
                    <?php if($message_type === 'success'): ?>
                        <div class="mb-4">
                            <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
                        </div>
                        <a href="login.php" class="btn btn-primary btn-action btn-lg">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Login Now
                        </a>
                    <?php else: ?>
                        <div class="mb-4">
                            <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <a href="forgot_password.php" class="btn btn-primary btn-action btn-lg">
                            <i class="bi bi-arrow-left me-2"></i>Request New Reset
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <div class="text-center">
                <p class="text-muted small mb-2">
                    Remember your password?
                    <a href="login.php" class="text-decoration-none">Back to Login</a>
                </p>
                <p class="text-muted small mb-0">
                    <i class="bi bi-shield-check me-1"></i>
                    Secure password reset powered by Klay Technologies
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
