<?php
/**
 * Simple SMTP Mailer for Brevo/Sendinblue
 * This implements basic SMTP functionality without external dependencies
 */

class SimpleSMTP {
    private $host;
    private $port;
    private $username;
    private $password;
    private $encryption;
    private $socket;
    private $debug = false;
    
    public function __construct($host, $port, $username, $password, $encryption = 'tls') {
        $this->host = $host;
        $this->port = $port;
        $this->username = $username;
        $this->password = $password;
        $this->encryption = $encryption;
    }
    
    public function setDebug($debug = true) {
        $this->debug = $debug;
    }
    
    private function log($message) {
        if ($this->debug) {
            error_log("SMTP: " . $message);
        }
    }
    
    public function sendMail($to, $subject, $body, $fromEmail, $fromName = '') {
        try {
            // Create socket connection
            $context = stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                ]
            ]);
            
            if ($this->encryption === 'ssl') {
                $this->socket = stream_socket_client("ssl://{$this->host}:{$this->port}", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
            } else {
                $this->socket = stream_socket_client("{$this->host}:{$this->port}", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
            }
            
            if (!$this->socket) {
                throw new Exception("Failed to connect to SMTP server: $errstr ($errno)");
            }
            
            $this->log("Connected to SMTP server");
            
            // Read initial response
            $response = fgets($this->socket, 512);
            $this->log("Server response: " . trim($response));
            
            if (substr($response, 0, 3) !== '220') {
                throw new Exception("SMTP server not ready: $response");
            }
            
            // Send EHLO
            $this->sendCommand("EHLO localhost");
            
            // Start TLS if required
            if ($this->encryption === 'tls') {
                $this->sendCommand("STARTTLS");
                if (!stream_socket_enable_crypto($this->socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                    throw new Exception("Failed to enable TLS encryption");
                }
                $this->log("TLS encryption enabled");
                
                // Send EHLO again after TLS
                $this->sendCommand("EHLO localhost");
            }
            
            // Authenticate
            $this->sendCommand("AUTH LOGIN");
            $this->sendCommand(base64_encode($this->username));
            $this->sendCommand(base64_encode($this->password));
            
            // Send email
            $this->sendCommand("MAIL FROM: <$fromEmail>");
            $this->sendCommand("RCPT TO: <$to>");
            $this->sendCommand("DATA");
            
            // Prepare headers
            $headers = "From: " . ($fromName ? "$fromName <$fromEmail>" : $fromEmail) . "\r\n";
            $headers .= "To: $to\r\n";
            $headers .= "Subject: $subject\r\n";
            $headers .= "MIME-Version: 1.0\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            $headers .= "Content-Transfer-Encoding: 8bit\r\n";
            $headers .= "Date: " . date('r') . "\r\n";
            $headers .= "Message-ID: <" . uniqid() . "@" . $this->host . ">\r\n";
            $headers .= "\r\n";
            
            // Send email data
            fwrite($this->socket, $headers . $body . "\r\n.\r\n");
            
            $response = fgets($this->socket, 512);
            $this->log("Data response: " . trim($response));
            
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("Failed to send email data: $response");
            }
            
            // Quit
            $this->sendCommand("QUIT");
            fclose($this->socket);
            
            $this->log("Email sent successfully to: $to");
            return true;
            
        } catch (Exception $e) {
            $this->log("Error: " . $e->getMessage());
            if ($this->socket) {
                fclose($this->socket);
            }
            return false;
        }
    }
    
    private function sendCommand($command) {
        fwrite($this->socket, $command . "\r\n");
        $response = fgets($this->socket, 512);
        $this->log("Command: $command");
        $this->log("Response: " . trim($response));
        
        $responseCode = substr($response, 0, 3);
        if (!in_array($responseCode, ['220', '221', '235', '250', '334', '354'])) {
            throw new Exception("SMTP command failed: $command - Response: $response");
        }
        
        return $response;
    }
}

/**
 * Send email using Brevo SMTP
 */
function sendEmailSMTP($to, $subject, $body, $fromEmail, $fromName = '') {
    // Brevo SMTP settings
    $smtp = new SimpleSMTP(
        'smtp-relay.brevo.com',
        587,
        '<EMAIL>',
        '7Nt8Aw9qOhKjCmYs',
        'tls'
    );
    
    // Enable debug for troubleshooting
    $smtp->setDebug(true);
    
    return $smtp->sendMail($to, $subject, $body, $fromEmail, $fromName);
}
?>
