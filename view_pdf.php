<?php
//include 'includes/header.php'; 
include 'config.php';

if (isset($_GET['id'])) {
    $invoice_id = $_GET['id'];
    $invoice = $pdo->query("SELECT * FROM invoices 
                           JOIN clients ON invoices.client_id = clients.id
                           WHERE invoices.id = $invoice_id")->fetch();

    $items = $pdo->query("SELECT * FROM invoice_items 
                         JOIN items ON invoice_items.item_id = items.id
                         WHERE invoice_id = $invoice_id")->fetchAll();

    // Calculate Totals
    $subtotal = 0;
    foreach($items as $item){
        $subtotal += $item['unit_price'] * $item['quantity'];
    }
    $tax = $subtotal * ($invoice['vat_rate'] / 100);
    $total = $subtotal + $tax;

    $invoice_date = date("d M Y", strtotime($invoice['invoice_date']));
    $due_date = date("d M Y", strtotime($invoice['due_date']));
?>

<style>
 table {
    width: 100%;
    border-collapse: collapse;
}
 table th {
    background-color: #f0f0f0;
    padding: 5pt;
    border: 1pt solid #ccc;
}
 table td {
    padding: 5pt;
    border: 1pt solid #ccc;
}
</style>

<div id="my-pdf-content" class="card">
    <div class="card-body">
        <h2>Invoice #<?= $invoice_id ?></h2>
        <br>
        <div class="row mb-4">
            <div class="col-md-6">
                <p><strong>Client:</strong> <?= $invoice['name'] ?></p>
                <p><strong>Email:</strong> <?= $invoice['email'] ?></p>
                <p><strong>Address:</strong> <?= $invoice['address'] ?></p>
            </div>
            <div class="col-md-6 text-end">
                <p><strong>Invoice Date:</strong> <?= $invoice_date ?></p>
                <p><strong>Due Date:</strong> <?= $due_date ?></p>
                <p><strong>Status:</strong> <?= ucfirst($invoice['status']) ?></p>
            </div>
        </div>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th style="text-align:left">Item</th>
                    <th style="text-align:left">Description</th>
                    <th style="text-align:right">Unit Price</th>
                    <th>Quantity</th>
                    <th style="text-align:right">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach($items as $item): ?>
                <tr>
                    <td><?= $item['name'] ?></td>
                    <td><?= $item['description'] ?></td>
                    <td style="text-align:right">$<?= number_format($item['unit_price'], 2) ?></td>
                    <td style="text-align:center"><?= $item['quantity'] ?></td>
                    <td style="text-align:right">$<?= number_format($item['unit_price'] * $item['quantity'], 2) ?></td>
                </tr>
                <?php endforeach; ?>
            <!-- </tbody>
        </table>

        <div class="row">
            <div class="col-md-4 offset-md-8">
                <table class="table"> -->
                    <tr>
                        <th style="text-align:right" colspan="4">Subtotal:</th>
                        <td style="text-align:right">$<?= number_format($subtotal, 2) ?></td>
                    </tr>
                    <tr>
                        <th style="text-align:right" colspan="4">VAT (<?= $invoice['vat_rate'] ?>%):</th>
                        <td style="text-align:right">$<?= number_format($tax, 2) ?></td>
                    </tr>
                    <tr>
                        <th style="text-align:right" colspan="4">Grand Total:</th>
                        <td style="text-align:right">$<?= number_format($total, 2) ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
} else {
    echo "No invoice ID provided.";
    exit;
}
?>

<?php //include 'includes/footer.php'; ?>