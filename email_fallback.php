<?php
/**
 * Email Fallback System
 * For development when SMTP is not working
 */

// Create emails directory if it doesn't exist
$emailDir = __DIR__ . '/emails';
if (!is_dir($emailDir)) {
    mkdir($emailDir, 0755, true);
}

/**
 * Save email to file instead of sending (for development)
 */
function saveEmailToFile($to, $subject, $body) {
    $emailDir = __DIR__ . '/emails';
    $filename = $emailDir . '/email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.html';
    
    $emailContent = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>$subject</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .email-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .email-header { background: #007bff; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .email-meta { background: #e9ecef; padding: 10px; margin-bottom: 20px; border-radius: 4px; font-size: 14px; }
        .email-body { line-height: 1.6; }
    </style>
</head>
<body>
    <div class='email-container'>
        <div class='email-header'>
            <h2>📧 Email Preview - Development Mode</h2>
        </div>
        <div class='email-meta'>
            <strong>To:</strong> $to<br>
            <strong>Subject:</strong> $subject<br>
            <strong>Date:</strong> " . date('Y-m-d H:i:s') . "<br>
            <strong>File:</strong> " . basename($filename) . "
        </div>
        <div class='email-body'>
            $body
        </div>
    </div>
</body>
</html>";
    
    file_put_contents($filename, $emailContent);
    
    // Log the email
    error_log("Email saved to file: $filename (To: $to, Subject: $subject)");
    
    return $filename;
}

/**
 * Enhanced email function with multiple fallback options
 */
function sendEmailWithFallback($to, $subject, $body, $isHTML = true) {
    // Try SMTP first
    if (function_exists('sendEmailSMTP')) {
        error_log("Attempting SMTP email to: $to");
        if (sendEmailSMTP($to, $subject, $body, '<EMAIL>', 'Klay Invoice System')) {
            error_log("SMTP email sent successfully");
            return true;
        }
        error_log("SMTP email failed, trying fallback methods");
    }
    
    // Try basic mail() function
    error_log("Attempting basic mail() function");
    $headers = array();
    $headers[] = 'MIME-Version: 1.0';
    $headers[] = 'Content-type: ' . ($isHTML ? 'text/html' : 'text/plain') . '; charset=UTF-8';
    $headers[] = 'From: Klay Invoice System <<EMAIL>>';
    $headers[] = 'Reply-To: <EMAIL>';
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    
    if (mail($to, $subject, $body, implode("\r\n", $headers))) {
        error_log("Basic mail() function succeeded");
        return true;
    }
    
    error_log("Basic mail() function failed, saving to file");
    
    // Fallback: Save to file for development
    $filename = saveEmailToFile($to, $subject, $body);
    
    // Create a notification file for easy access
    $notificationFile = __DIR__ . '/emails/latest_email.txt';
    file_put_contents($notificationFile, "Latest email saved: $filename\nTo: $to\nSubject: $subject\nTime: " . date('Y-m-d H:i:s'));
    
    return true; // Return true so registration doesn't fail
}

/**
 * View saved emails (for development)
 */
function getEmailList() {
    $emailDir = __DIR__ . '/emails';
    if (!is_dir($emailDir)) {
        return [];
    }
    
    $emails = [];
    $files = glob($emailDir . '/email_*.html');
    
    foreach ($files as $file) {
        $emails[] = [
            'filename' => basename($file),
            'path' => $file,
            'date' => date('Y-m-d H:i:s', filemtime($file)),
            'size' => filesize($file)
        ];
    }
    
    // Sort by date (newest first)
    usort($emails, function($a, $b) {
        return filemtime($b['path']) - filemtime($a['path']);
    });
    
    return $emails;
}
?>
