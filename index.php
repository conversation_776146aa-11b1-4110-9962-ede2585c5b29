<?php
// Include auth check
require_once 'auth.php';

// Only proceed if user is logged in
if(isset($_SESSION['user_id'])) {
    $page_title = "Klay Invoice - Dashboard";
    include 'includes/header.php'; 
    include 'config.php'; 

    // Calculate monthly statistics
    $currentMonth = date('Y-m');

    // Count of invoices created this month (based on invoice_date)
    $monthlyInvoices = $pdo->query("SELECT COUNT(*) FROM invoices 
                                   WHERE DATE_FORMAT(invoice_date, '%Y-%m') = '$currentMonth'")->fetchColumn();

    // Count of paid invoices this month (based on paid_date)
    $paidInvoicesThisMonth = $pdo->query("SELECT COUNT(*) 
                                         FROM invoices 
                                         WHERE DATE_FORMAT(paid_date, '%Y-%m') = '$currentMonth'
                                         AND payment_status = 'paid'")->fetchColumn();

    // Total revenue this month WITHOUT VAT (based on paid_date)
    $monthlyRevenue = $pdo->query("SELECT SUM(total_after_discount) FROM (
                              SELECT 
                                (SUM(unit_price * quantity) - 
                                (SUM(unit_price * quantity) * (discount_rate/100)) - 
                                discount_fixed) AS total_after_discount
                              FROM invoices 
                              JOIN invoice_items ON invoices.id = invoice_items.invoice_id 
                              WHERE DATE_FORMAT(paid_date, '%Y-%m') = '$currentMonth'
                              AND payment_status = 'paid'
                              GROUP BY invoices.id
                          ) AS totals")->fetchColumn();
?>

<!-- Dashboard Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1 text-gradient">Dashboard Overview</h1>
                <p class="text-muted mb-0">Welcome back, <?php echo htmlspecialchars($_SESSION['username']); ?>! Here's what's happening with your invoices.</p>
            </div>
            <div>
                <a href="create_invoice.php" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>Create New Invoice
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-5">
    <div class="col-xl-3 col-md-6">
        <div class="stats-card card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-file-earmark-text text-primary fs-1"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="card-title text-muted mb-1 text-uppercase fw-medium">Total Invoices</h6>
                        <h2 class="mb-0 fw-bold text-dark"><?= number_format($pdo->query("SELECT COUNT(*) FROM invoices")->fetchColumn(), 0) ?></h2>
                        <div class="small text-muted mt-1">
                            <i class="bi bi-file-earmark-text me-1"></i>All time invoices
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="stats-card success card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-check-circle text-success fs-1"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="card-title text-muted mb-1 text-uppercase fw-medium">Paid Invoices</h6>
                        <?php
                        // Get paid invoices total WITHOUT VAT and with discounts applied
                        $paidInvoices = $pdo->query("SELECT SUM(total) FROM (
                            SELECT
                                (SUM(unit_price * quantity) -
                                (SUM(unit_price * quantity) * (discount_rate/100)) -
                                discount_fixed) AS total
                            FROM invoices
                            JOIN invoice_items ON invoices.id = invoice_items.invoice_id
                            WHERE payment_status = 'paid'
                            GROUP BY invoices.id
                        ) AS totals")->fetchColumn();

                        $paidCount = $pdo->query("SELECT COUNT(*) FROM invoices WHERE payment_status = 'paid'")->fetchColumn();
                        ?>
                        <h2 class="mb-0 fw-bold text-dark">$<?= number_format($paidInvoices ?? 0, 2) ?></h2>
                        <div class="small text-muted mt-1">
                            <i class="bi bi-check-circle me-1"></i><?= number_format($paidCount, 0) ?> invoices paid
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="stats-card danger card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-danger bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-exclamation-circle text-danger fs-1"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="card-title text-muted mb-1 text-uppercase fw-medium">Unpaid Invoices</h6>
                        <?php
                        // Get unpaid invoices total WITHOUT VAT and with discounts applied
                        $unpaidInvoices = $pdo->query("SELECT SUM(total) FROM (
                            SELECT
                                (SUM(unit_price * quantity) -
                                (SUM(unit_price * quantity) * (discount_rate/100)) -
                                discount_fixed) AS total
                            FROM invoices
                            JOIN invoice_items ON invoices.id = invoice_items.invoice_id
                            WHERE payment_status = 'unpaid'
                            GROUP BY invoices.id
                        ) AS totals")->fetchColumn();

                        $unpaidCount = $pdo->query("SELECT COUNT(*) FROM invoices WHERE payment_status = 'unpaid'")->fetchColumn();
                        ?>
                        <h2 class="mb-0 fw-bold text-dark">$<?= number_format($unpaidInvoices ?? 0, 2) ?></h2>
                        <div class="small text-muted mt-1">
                            <i class="bi bi-exclamation-circle me-1"></i><?= number_format($unpaidCount, 0) ?> invoices pending
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="stats-card warning card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-hourglass-split text-warning fs-1"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="card-title text-muted mb-1 text-uppercase fw-medium">Processing</h6>
                        <?php
                        // Get processing invoices total WITHOUT VAT and with discounts applied
                        $processingInvoices = $pdo->query("SELECT SUM(total) FROM (
                            SELECT
                                (SUM(unit_price * quantity) -
                                (SUM(unit_price * quantity) * (discount_rate/100)) -
                                discount_fixed) AS total
                            FROM invoices
                            JOIN invoice_items ON invoices.id = invoice_items.invoice_id
                            WHERE payment_status = 'processing'
                            GROUP BY invoices.id
                        ) AS totals")->fetchColumn();

                        $processingCount = $pdo->query("SELECT COUNT(*) FROM invoices WHERE payment_status = 'processing'")->fetchColumn();
                        ?>
                        <h2 class="mb-0 fw-bold text-dark">$<?= number_format($processingInvoices ?? 0, 2) ?></h2>
                        <div class="small text-muted mt-1">
                            <i class="bi bi-hourglass-split me-1"></i><?= number_format($processingCount, 0) ?> in progress
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Performance Section -->
<div class="row g-4 mb-5">
    <div class="col-12">
        <h3 class="h4 mb-3 text-dark">
            <i class="bi bi-calendar-month text-primary me-2"></i>
            Monthly Performance - <?= date('F Y') ?>
        </h3>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-file-earmark-plus text-primary fs-2"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="card-title text-muted mb-1 fw-medium">Invoices Created</h6>
                        <a href="invoices.php?status=invoiced&date_from=<?= date('Y-m-01') ?>&date_to=<?= date('Y-m-d') ?>" class="text-decoration-none">
                            <h3 class="mb-1 fw-bold text-dark"><?= number_format($monthlyInvoices, 0) ?></h3>
                            <p class="text-muted mb-0 small">
                                <i class="bi bi-calendar3 me-1"></i>This Month
                                <span class="badge bg-light text-dark ms-2"><?= date('M Y') ?></span>
                            </p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-check-circle text-success fs-2"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="card-title text-muted mb-1 fw-medium">Payments Received</h6>
                        <a href="invoices.php?status=paid&date_from=<?= date('Y-m-01') ?>&date_to=<?= date('Y-m-d') ?>" class="text-decoration-none">
                            <h3 class="mb-1 fw-bold text-dark"><?= number_format($paidInvoicesThisMonth ?? 0, 0) ?></h3>
                            <p class="text-muted mb-0 small">
                                <i class="bi bi-cash-coin me-1"></i>Payments
                                <span class="badge bg-success bg-opacity-10 text-success ms-2">Received</span>
                            </p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-cash-stack text-warning fs-2"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="card-title text-muted mb-1 fw-medium">Total Revenue</h6>
                        <h3 class="mb-1 fw-bold text-dark">$<?= number_format($monthlyRevenue ?? 0, 2) ?></h3>
                        <p class="text-muted mb-0 small">
                            <i class="bi bi-graph-up me-1"></i>Monthly Revenue
                            <span class="badge bg-info bg-opacity-10 text-info ms-2">Excl. VAT</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Tables Section -->
<div class="row g-4">
    <div class="col-12">
        <h3 class="h4 mb-3 text-dark">
            <i class="bi bi-graph-up text-primary me-2"></i>
            Recent Activity & Analytics
        </h3>
    </div>

    <div class="col-lg-7">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fw-semibold">
                        <i class="bi bi-file-earmark-text me-2"></i>Recent Invoices
                    </h5>
                    <span class="badge bg-light text-primary">Latest 5</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover table-no-border mb-0">
                        <thead>
                            <tr>
                                <th class="border-0 ps-4">Invoice</th>
                                <th class="border-0">Client</th>
                                <th class="border-0">Date</th>
                                <th class="border-0">Status</th>
                                <th class="border-0 text-end pe-4">Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $recentInvoices = $pdo->query("SELECT i.id, c.name AS client_name,
                                                          (SELECT
                                                              (SUM(unit_price * quantity) -
                                                              (SUM(unit_price * quantity) * (i.discount_rate/100)) -
                                                              i.discount_fixed)
                                                           FROM invoice_items
                                                           WHERE invoice_id = i.id) AS total_price,
                                                          i.payment_status, i.quote_date
                                                          FROM invoices i
                                                          JOIN clients c ON i.client_id = c.id
                                                          ORDER BY i.id DESC LIMIT 5")->fetchAll();

                            if (empty($recentInvoices)) {
                                echo "<tr><td colspan='5' class='text-center py-4 text-muted'>
                                        <i class='bi bi-inbox fs-1 d-block mb-2'></i>
                                        No invoices found. <a href='create_invoice.php' class='text-decoration-none'>Create your first invoice</a>
                                      </td></tr>";
                            } else {
                                foreach ($recentInvoices as $invoice) {
                                    $statusClass = '';
                                    $statusIcon = '';
                                    switch ($invoice['payment_status']) {
                                        case 'paid':
                                            $statusClass = 'success';
                                            $statusIcon = 'check-circle';
                                            break;
                                        case 'unpaid':
                                            $statusClass = 'danger';
                                            $statusIcon = 'exclamation-circle';
                                            break;
                                        case 'processing':
                                            $statusClass = 'warning';
                                            $statusIcon = 'hourglass-split';
                                            break;
                                        default:
                                            $statusClass = 'secondary';
                                            $statusIcon = 'question-circle';
                                    }

                                    echo "<tr>";
                                    echo "<td class='ps-4'><span class='fw-semibold text-primary'>#INV-{$invoice['id']}</span></td>";
                                    echo "<td><span class='fw-medium'>{$invoice['client_name']}</span></td>";
                                    echo "<td><span class='text-muted small'>" . date('d M Y', strtotime($invoice['quote_date'])) . "</span></td>";
                                    echo "<td><span class='badge bg-{$statusClass} bg-opacity-10 text-{$statusClass} border border-{$statusClass} border-opacity-25'>
                                            <i class='bi bi-{$statusIcon} me-1'></i>" . ucfirst($invoice['payment_status']) . "</span></td>";
                                    echo "<td class='text-end pe-4'><span class='fw-semibold'>$" . number_format($invoice['total_price'], 2) . "</span></td>";
                                    echo "</tr>";
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer bg-light border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted small">Showing latest 5 invoices</span>
                        <a href="invoices.php" class="btn btn-primary btn-sm">
                            <i class="bi bi-arrow-right me-1"></i>View All Invoices
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-5">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fw-semibold">
                        <i class="bi bi-people me-2"></i>Top Clients
                    </h5>
                    <span class="badge bg-light text-primary">Paid Invoices</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover table-no-border mb-0">
                        <thead>
                            <tr>
                                <th class="border-0 ps-4">Client</th>
                                <th class="border-0 text-center">Invoices</th>
                                <th class="border-0 text-end pe-4">Total Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $topClients = $pdo->query("SELECT c.id, c.name,
                                                      COUNT(i.id) AS invoice_count,
                                                      SUM(item_totals.total) AS total_value
                                                      FROM clients c
                                                      JOIN invoices i ON c.id = i.client_id AND i.payment_status = 'paid'
                                                      JOIN (
                                                          SELECT invoice_id,
                                                              (SUM(unit_price * quantity) -
                                                              (SUM(unit_price * quantity) * (invoices.discount_rate/100)) -
                                                              invoices.discount_fixed) AS total
                                                          FROM invoice_items
                                                          JOIN invoices ON invoice_items.invoice_id = invoices.id
                                                          WHERE invoices.payment_status = 'paid'
                                                          GROUP BY invoice_id
                                                      ) AS item_totals ON i.id = item_totals.invoice_id
                                                      GROUP BY c.id, c.name
                                                      ORDER BY total_value DESC
                                                      LIMIT 5")->fetchAll();

                            if (empty($topClients)) {
                                echo "<tr><td colspan='3' class='text-center py-4 text-muted'>
                                        <i class='bi bi-people fs-1 d-block mb-2'></i>
                                        No clients found. <a href='clients.php' class='text-decoration-none'>Add your first client</a>
                                      </td></tr>";
                            } else {
                                foreach ($topClients as $index => $client) {
                                    $rankBadge = '';
                                    switch($index) {
                                        case 0: $rankBadge = '<span class="badge bg-warning text-dark me-2"><i class="bi bi-trophy"></i></span>'; break;
                                        case 1: $rankBadge = '<span class="badge bg-secondary text-white me-2"><i class="bi bi-award"></i></span>'; break;
                                        case 2: $rankBadge = '<span class="badge bg-warning bg-opacity-50 text-dark me-2"><i class="bi bi-star"></i></span>'; break;
                                        default: $rankBadge = '<span class="text-muted me-2">' . ($index + 1) . '.</span>';
                                    }

                                    echo "<tr>";
                                    echo "<td class='ps-4'>{$rankBadge}<span class='fw-medium'>{$client['name']}</span></td>";
                                    echo "<td class='text-center'><span class='badge bg-primary bg-opacity-10 text-primary'>{$client['invoice_count']}</span></td>";
                                    echo "<td class='text-end pe-4'><span class='fw-semibold'>$" . number_format($client['total_value'], 2) . "</span></td>";
                                    echo "</tr>";
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer bg-light border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted small">Ranked by paid invoice revenue</span>
                        <a href="clients.php" class="btn btn-primary btn-sm">
                            <i class="bi bi-arrow-right me-1"></i>View All Clients
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
<?php
} else {
    // If somehow we got here without being logged in, redirect to login
    header("Location: login.php");
    exit();
}
?>
