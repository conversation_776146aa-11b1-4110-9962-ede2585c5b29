<?php
require_once 'auth.php';
include 'config.php';

// Process form submissions BEFORE any output
$bin = filter_input(INPUT_POST, 'bin', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
$address = filter_input(INPUT_POST, 'address', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

if(!filter_var($email, FILTER_VALIDATE_EMAIL) && !empty($email)) {
    $client_error = "Invalid email format!";
}

// Add Client
if(isset($_POST['add_client']) && (empty($email) || filter_var($email, FILTER_VALIDATE_EMAIL))){
    $stmt = $pdo->prepare("INSERT INTO clients (bin, name, email, address) VALUES (?, ?, ?, ?)");
    $stmt->execute([$bin, $name, $email, $address]);
    $client_success = "Client added successfully!";
}

// Handle deletion
if(isset($_POST['delete_client'])){
    try {
        $stmt = $pdo->prepare("DELETE FROM clients WHERE id = ?");
        $stmt->execute([$_POST['delete_id']]);
        $delete_success = "Client deleted successfully!";
    } catch(PDOException $e) {
        $delete_error = "Cannot delete client with existing invoices!";
    }
}

// Set page title and include header AFTER form processing
$page_title = "Klay Invoice - Clients";
include 'includes/header.php';

// Display success/error messages
if(isset($client_error)) {
    echo "<div class='alert alert-danger text-center' role='alert'><i class='bi bi-exclamation-triangle-fill'></i> $client_error</div>";
}
if(isset($client_success)) {
    echo "<div class='alert alert-success text-center' role='alert'><i class='bi bi-check-circle-fill'></i> $client_success</div>";
}
if(isset($delete_error)) {
    echo "<div class='alert alert-danger text-center' role='alert'><i class='bi bi-exclamation-triangle-fill'></i> $delete_error</div>";
}
if(isset($delete_success)) {
    echo "<div class='alert alert-success text-center' role='alert'><i class='bi bi-check-circle-fill'></i> $delete_success</div>";
}

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchCondition = '';
$params = [];

if (!empty($search)) {
    $searchCondition = " WHERE name LIKE :search OR bin LIKE :search OR email LIKE :search OR address LIKE :search";
    $params[':search'] = "%$search%";
}

// Get Clients with search
$stmt = $pdo->prepare("SELECT * FROM clients $searchCondition ORDER BY name ASC");
if (!empty($params)) {
    foreach ($params as $param => $value) {
        $stmt->bindValue($param, $value);
    }
}
$stmt->execute();
$clients = $stmt->fetchAll();
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-people"></i> Clients</h2>
        </div>
    </div>

    <!-- Search Bar -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-search"></i> Search Clients</h5>
        </div>
        <div class="card-body">
            <form method="get" class="mb-0">
                <div class="input-group">
                    <input type="text" name="search" class="form-control" placeholder="Search by name, BIN, email or address..." 
                           value="<?= htmlspecialchars($search) ?>">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i> Search
                    </button>
                    <?php if(!empty($search)): ?>
                    <a href="clients.php" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle"></i> Clear
                    </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Client Form -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-person-plus"></i> Add New Client</h5>
        </div>
        <div class="card-body">
            <form method="post">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Client Name</label>
                            <input type="text" id="name" name="name" class="form-control" placeholder="Client Name" required>
                        </div>
                        <div class="mb-3">
                            <label for="bin" class="form-label">BIN</label>
                            <input type="text" id="bin" name="bin" class="form-control" placeholder="Business Identification Number">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" id="email" name="email" class="form-control" placeholder="Email Address">
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea id="address" name="address" class="form-control" placeholder="Physical Address" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="text-end">
                    <button type="submit" name="add_client" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Client
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Clients Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0"><i class="bi bi-people"></i> Client List</h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-primary"><?= count($clients) ?> clients found</span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th class="ps-3" width="5%">ID</th>
                            <th width="30%">Client Information</th>
                            <th width="40%">Contact Details</th>
                            <th class="text-center" width="25%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(count($clients) > 0): ?>
                            <?php foreach($clients as $client): ?>
                            <tr>
                                <td class="ps-3"><?= $client['id'] ?></td>
                                <td>
                                    <strong><?= htmlspecialchars($client['name']) ?></strong>
                                    <?php if(!empty($client['bin'])): ?>
                                        <br><span class="badge bg-secondary">BIN: <?= htmlspecialchars($client['bin']) ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if(!empty($client['address'])): ?>
                                        <i class="bi bi-geo-alt text-muted"></i> <?= htmlspecialchars($client['address']) ?><br>
                                    <?php endif; ?>
                                    <?php if(!empty($client['email'])): ?>
                                        <i class="bi bi-envelope text-muted"></i> <a href="mailto:<?= htmlspecialchars($client['email']) ?>"><?= htmlspecialchars($client['email']) ?></a>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group">
                                        <a href="edit_client.php?id=<?= $client['id'] ?>" class="btn btn-sm btn-warning" title="Edit Client">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $client['id'] ?>" title="Delete Client">
                                            <i class="bi bi-trash"></i> Delete
                                        </button>
                                    </div>
                                    
                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal<?= $client['id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $client['id'] ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel<?= $client['id'] ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body text-start">
                                                    <p>Are you sure you want to delete client: <strong><?= htmlspecialchars($client['name']) ?></strong>?</p>
                                                    <p class="text-danger"><strong>Warning:</strong> This action cannot be undone. If this client has invoices, the deletion will fail.</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form method="post" style="display:inline;">
                                                        <input type="hidden" name="delete_id" value="<?= $client['id'] ?>">
                                                        <button type="submit" name="delete_client" class="btn btn-danger">Delete Client</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <i class="bi bi-info-circle text-info fs-4"></i><br>
                                    No clients found. Add a new client using the form above.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
