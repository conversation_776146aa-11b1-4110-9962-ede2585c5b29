<?php
require_once 'auth.php';
$page_title = "Klay Invoice - Settings";
include 'includes/header.php'; 
require_once 'config.php';

// Handle password change
if(isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Get current user
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if(!$user || !password_verify($current_password, $user['password'])) {
        $password_error = "Current password is incorrect";
    } else if($new_password !== $confirm_password) {
        $password_error = "New passwords do not match";
    } else if(strlen($new_password) < 6) {
        $password_error = "New password must be at least 6 characters";
    } else {
        // Update password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([$hashed_password, $_SESSION['user_id']]);
        $password_success = "Password updated successfully";
    }
}

// Generate the backup file
//$backup_file = 'backup.sql';
//exec("mysqldump -h " . $host . " -u " . $user . " -p" . $pass . " " . $db . " > " . $backup_file);

?>

<h1>Settings</h1>
<hr>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-lock"></i> Change Password</h5>
            </div>
            <div class="card-body">
                <?php if(isset($password_error)): ?>
                    <div class="alert alert-danger"><?php echo $password_error; ?></div>
                <?php endif; ?>
                
                <?php if(isset($password_success)): ?>
                    <div class="alert alert-success"><?php echo $password_success; ?></div>
                <?php endif; ?>
                
                <form method="post">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <button type="submit" name="change_password" class="btn btn-primary">Change Password</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-database"></i> Database Backup</h5>
            </div>
            <div class="card-body">
                <p>Create and download a backup of your entire database.</p>
                <a class="btn btn-primary" href="backup.php">Download Backup</a>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
