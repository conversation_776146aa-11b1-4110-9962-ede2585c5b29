<?php
require_once 'auth.php';
require_once 'config.php';

// Get current user data
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

// Get user settings
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM user_settings WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$settings_data = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Handle company information update
if(isset($_POST['update_company'])) {
    $company_name = trim($_POST['company_name']);
    $company_address = trim($_POST['company_address']);
    $company_phone = trim($_POST['company_phone']);
    $company_email = trim($_POST['company_email']);
    $company_website = trim($_POST['company_website']);

    $stmt = $pdo->prepare("UPDATE users SET company_name = ?, company_address = ?, company_phone = ?, company_email = ?, company_website = ? WHERE id = ?");
    if($stmt->execute([$company_name, $company_address, $company_phone, $company_email, $company_website, $_SESSION['user_id']])) {
        $company_success = "Company information updated successfully";
        // Refresh user data
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
    } else {
        $company_error = "Error updating company information";
    }
}

// Handle invoice settings update
if(isset($_POST['update_settings'])) {
    $settings = [
        'invoice_prefix' => $_POST['invoice_prefix'],
        'default_vat_rate' => $_POST['default_vat_rate'],
        'currency_symbol' => $_POST['currency_symbol'],
        'date_format' => $_POST['date_format'],
        'invoice_footer_text' => $_POST['invoice_footer_text']
    ];

    $stmt = $pdo->prepare("INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");

    $all_updated = true;
    foreach($settings as $key => $value) {
        if(!$stmt->execute([$_SESSION['user_id'], $key, $value])) {
            $all_updated = false;
        }
    }

    if($all_updated) {
        $settings_success = "Invoice settings updated successfully";
        // Refresh settings data
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM user_settings WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $settings_data = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    } else {
        $settings_error = "Error updating some settings";
    }
}

// Handle password change
if(isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    if(!password_verify($current_password, $user['password'])) {
        $password_error = "Current password is incorrect";
    } else if($new_password !== $confirm_password) {
        $password_error = "New passwords do not match";
    } else if(strlen($new_password) < 6) {
        $password_error = "New password must be at least 6 characters";
    } else {
        // Update password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([$hashed_password, $_SESSION['user_id']]);
        $password_success = "Password updated successfully";
    }
}

// Handle session messages (from resend verification)
$session_message = '';
$session_message_type = '';
if (isset($_SESSION['message'])) {
    $session_message = $_SESSION['message'];
    $session_message_type = $_SESSION['message_type'];
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

// Set page title and include header AFTER form processing
$page_title = "Klay Invoice - Settings";
include 'includes/header.php';

// Generate the backup file
//$backup_file = 'backup.sql';
//exec("mysqldump -h " . $host . " -u " . $user . " -p" . $pass . " " . $db . " > " . $backup_file);

?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-gear"></i> Account Settings</h2>
        </div>
    </div>

    <!-- Session Messages -->
    <?php if(!empty($session_message)): ?>
        <div class="alert alert-<?= $session_message_type ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?= $session_message_type === 'success' ? 'check-circle' : ($session_message_type === 'info' ? 'info-circle' : 'exclamation-triangle') ?> me-2"></i>
            <?= htmlspecialchars($session_message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Company Information Section -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-primary text-dark">
            <h5 class="mb-0"><i class="bi bi-building me-2"></i>Company Information</h5>
        </div>
        <div class="card-body">
            <?php if(isset($company_error)): ?>
                <div class="alert alert-danger"><i class="bi bi-exclamation-triangle me-2"></i><?= $company_error ?></div>
            <?php endif; ?>
            <?php if(isset($company_success)): ?>
                <div class="alert alert-success"><i class="bi bi-check-circle me-2"></i><?= $company_success ?></div>
            <?php endif; ?>

            <form method="post">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="company_name" class="form-label">Company Name *</label>
                        <input type="text" class="form-control" id="company_name" name="company_name"
                               value="<?= htmlspecialchars($user['company_name'] ?? '') ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="company_email" class="form-label">Company Email</label>
                        <input type="email" class="form-control" id="company_email" name="company_email"
                               value="<?= htmlspecialchars($user['company_email'] ?? '') ?>">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="company_phone" class="form-label">Company Phone</label>
                        <input type="text" class="form-control" id="company_phone" name="company_phone"
                               value="<?= htmlspecialchars($user['company_phone'] ?? '') ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="company_website" class="form-label">Company Website</label>
                        <input type="url" class="form-control" id="company_website" name="company_website"
                               value="<?= htmlspecialchars($user['company_website'] ?? '') ?>" placeholder="https://example.com">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="company_address" class="form-label">Company Address</label>
                    <textarea class="form-control" id="company_address" name="company_address" rows="3"><?= htmlspecialchars($user['company_address'] ?? '') ?></textarea>
                </div>
                <button type="submit" name="update_company" class="btn btn-primary">
                    <i class="bi bi-check me-2"></i>Update Company Information
                </button>
            </form>
        </div>
    </div>

    <!-- Invoice Settings Section -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-success text-dark">
            <h5 class="mb-0"><i class="bi bi-file-earmark-text me-2"></i>Invoice Settings</h5>
        </div>
        <div class="card-body">
            <?php if(isset($settings_error)): ?>
                <div class="alert alert-danger"><i class="bi bi-exclamation-triangle me-2"></i><?= $settings_error ?></div>
            <?php endif; ?>
            <?php if(isset($settings_success)): ?>
                <div class="alert alert-success"><i class="bi bi-check-circle me-2"></i><?= $settings_success ?></div>
            <?php endif; ?>

            <form method="post">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="invoice_prefix" class="form-label">Invoice Prefix</label>
                        <input type="text" class="form-control" id="invoice_prefix" name="invoice_prefix"
                               value="<?= htmlspecialchars($settings_data['invoice_prefix'] ?? 'INV-') ?>" placeholder="INV-">
                        <div class="form-text">This will appear before invoice numbers (e.g., INV-001)</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="default_vat_rate" class="form-label">Default VAT Rate (%)</label>
                        <input type="number" class="form-control" id="default_vat_rate" name="default_vat_rate"
                               value="<?= htmlspecialchars($settings_data['default_vat_rate'] ?? '5.00') ?>" step="0.01" min="0" max="100">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="currency_symbol" class="form-label">Currency Symbol</label>
                        <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                               value="<?= htmlspecialchars($settings_data['currency_symbol'] ?? 'BDT') ?>" placeholder="BDT">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="date_format" class="form-label">Date Format</label>
                        <select class="form-select form-control" id="date_format" name="date_format">
                            <option value="d M Y" <?= ($settings_data['date_format'] ?? 'd M Y') === 'd M Y' ? 'selected' : '' ?>>15 Jan 2024</option>
                            <option value="m/d/Y" <?= ($settings_data['date_format'] ?? '') === 'm/d/Y' ? 'selected' : '' ?>>01/15/2024</option>
                            <option value="d/m/Y" <?= ($settings_data['date_format'] ?? '') === 'd/m/Y' ? 'selected' : '' ?>>15/01/2024</option>
                            <option value="Y-m-d" <?= ($settings_data['date_format'] ?? '') === 'Y-m-d' ? 'selected' : '' ?>>2024-01-15</option>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="invoice_footer_text" class="form-label">Invoice Footer Text</label>
                    <textarea class="form-control" id="invoice_footer_text" name="invoice_footer_text" rows="2"
                              placeholder="Thank you for your business!"><?= htmlspecialchars($settings_data['invoice_footer_text'] ?? 'Thank you for your business!') ?></textarea>
                    <div class="form-text">This text will appear at the bottom of your invoices</div>
                </div>
                <button type="submit" name="update_settings" class="btn btn-success">
                    <i class="bi bi-check me-2"></i>Update Invoice Settings
                </button>
            </form>
        </div>
    </div>

    <!-- Password and Account Info Row -->
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="bi bi-lock me-2"></i>Change Password</h5>
                </div>
                <div class="card-body">
                    <?php if(isset($password_error)): ?>
                        <div class="alert alert-danger"><i class="bi bi-exclamation-triangle me-2"></i><?= $password_error ?></div>
                    <?php endif; ?>
                    <?php if(isset($password_success)): ?>
                        <div class="alert alert-success"><i class="bi bi-check-circle me-2"></i><?= $password_success ?></div>
                    <?php endif; ?>

                    <form method="post">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <button type="submit" name="change_password" class="btn btn-warning">
                            <i class="bi bi-check me-2"></i>Update Password
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-info text-dark">
                    <h5 class="mb-0"><i class="bi bi-person me-2"></i>Account Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Full Name</label>
                        <p class="form-control-plaintext"><?= htmlspecialchars($user['full_name']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Email</label>
                        <p class="form-control-plaintext">
                            <?= htmlspecialchars($user['email']) ?>
                            <?php if($user['email_verified']): ?>
                                <span class="badge bg-success ms-2"><i class="bi bi-check-circle me-1"></i>Verified</span>
                            <?php else: ?>
                                <span class="badge bg-warning ms-2"><i class="bi bi-exclamation-triangle me-1"></i>Not Verified</span>
                                <br>
                                <a href="resend_verification.php" class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="bi bi-envelope me-1"></i>Resend Verification Email
                                </a>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Member Since</label>
                        <p class="form-control-plaintext"><?= !empty($user['created_at']) ? date('d M Y', strtotime($user['created_at'])) : 'Not Set' ?></p>
                    </div>
                    <div class="mb-0">
                        <label class="form-label fw-bold">Account Status</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>Active</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
