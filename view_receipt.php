<?php
//include 'includes/header.php'; 
include 'config.php';

if (isset($_GET['id'])) {
    $invoice_id = $_GET['id'];
    $invoice = $pdo->query("SELECT * FROM invoices 
                           JOIN clients ON invoices.client_id = clients.id
                           WHERE invoices.id = $invoice_id")->fetch();

    $items = $pdo->query("SELECT * FROM invoice_items 
                         JOIN items ON invoice_items.item_id = items.id
                         WHERE invoice_id = $invoice_id")->fetchAll();

    // Calculate Totals
    $subtotal = 0;
    foreach ($items as $item){
        $subtotal += $item['unit_price'] * $item['quantity'];
    }
    
    $discount_rate = $subtotal * ($invoice['discount_rate'] / 100);
    $discount_fixed = $invoice['discount_fixed'];
    $total = $subtotal - ($discount_rate + $discount_fixed);
    $tax = $total * ($invoice['vat_rate'] / 100);
    $total_grand = $total + $tax;

    $invoice_for = $invoice['invoice_for'];
    $service_type = $invoice['service_type'];
    $special_notes = $item['special_notes'];

    $invoice_date = date("d/m/Y", strtotime($invoice['invoice_date']));
    $invoice_id_date = date("Y", strtotime($invoice['invoice_date']));
    //$due_date = date("d M Y", strtotime($invoice['due_date']));

    function numberToWords($number) {
        $ones = array('Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine');
        $teens = array('Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen');
        $tens = array('Ten', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety');
        $hundreds = array('Hundred', 'Thousand', 'Million', 'Billion');
    
        $number = round($number, 2); // Round the number to 2 decimal places
    
        $dollars = intval($number);
        $cents = round(($number - $dollars) * 100);
    
        $words = '';
    
        if ($dollars > 0) {
            $words .= numberToWordsHelper($dollars) . ' Taka ';
        }
    
        if ($cents > 0) {
            $words .= 'and ' . numberToWordsHelper($cents) . ' Poisha';
        }
    
        return trim($words);
    }
    
    function numberToWordsHelper($number) {
        $ones = array('Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine');
        $teens = array('Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen');
        $tens = array('Ten', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety');
        $hundreds = array('Hundred', 'Thousand', 'Million', 'Billion');
    
        if ($number < 10) {
            return $ones[$number];
        } elseif ($number < 20) {
            return $teens[$number - 11];
        } elseif ($number < 100) {
            return $tens[intval($number / 10) - 1] . ' ' . ($number % 10 == 0 ? '' : numberToWordsHelper($number % 10));
        } elseif ($number < 1000) {
            return $ones[intval($number / 100)] . ' ' . $hundreds[0] . ' ' . ($number % 100 == 0 ? '' : numberToWordsHelper($number % 100));
        } elseif ($number < 1000000) {
            return numberToWordsHelper(intval($number / 1000)) . ' ' . $hundreds[1] . ' ' . ($number % 1000 == 0 ? '' : numberToWordsHelper($number % 1000));
        } elseif ($number < 1000000000) {
            return numberToWordsHelper(intval($number / 1000000)) . ' ' . $hundreds[2] . ' ' . ($number % 1000000 == 0 ? '' : numberToWordsHelper($number % 1000000));
        } elseif ($number < 1000000000000) {
            return numberToWordsHelper(intval($number / 1000000000)) . ' ' . $hundreds[3] . ' ' . ($number % 1000000000 == 0 ? '' : numberToWordsHelper($number % 1000000000));
        }
    }
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo "Receipt: " . $_GET['id']; ?></title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap">

        <style>
            body {
                font-family: 'Poppins', sans-serif;
                font-size: 13px;
            }
            .print-wrap {
                max-width: 1000px;
                border: 5px solid #000;
                padding: 15px;
                position: relative;
            }
            .brand-logo {
                position: absolute;
                top: 15px;
                right: 15px;
            }
            .cl-info label {
                display: block;
                line-height: 1.6em;
                border-bottom: 1px solid #ddd;
                padding-top: 3px;
                padding-bottom: 3px;
            }
            .kl-amount * {
                border-color: #999;
                font-size: 15px;
            }
            .kl-amount td,
            .kl-amount th {
                padding: 5px 15px;
            }
            .kl-amount th {
                background-color: #F5F5F5;
                font-weight: 700;
            }
            .kl-payment-by .form-check {
                display: inline-block;
                margin-right: 20px;
            }
        </style>
    </head>

    <body>
    <div class="container-fluid print-wrap mt-2 mb-5">
        <div class="kl-head">
            <h3 class="fw-bold mb-0">
                PAYMENT RECEIPT
            </h3>
            <label style="font-size: 12px;"><strong>RECEIPT NO:</strong> <?= $invoice_id_date ?><?= $invoice_id ?></label><br>
            <label style="font-size: 12px;"><strong>DATE:</strong> <?= $invoice_date ?></label>
        </div>
        <img src="assets/img/logo.svg" class="brand-logo img-fluid" width="300">
        
        <div class="mt-3 cl-info"> 
            <label><strong>RECEIVED FROM:</strong> <?= $invoice['name'] ?></label>            <label><strong>ADDRESS:</strong> <?= $invoice['address'] ?></label>
            <label><strong>PURPOSE OF PAYMENT:</strong> <?= $invoice['invoice_for'] ?> - <?= $invoice['service_type'] ?></label>
            <label><strong>AMOUNT (In Words):</strong> <?php echo numberToWords($total_grand); ?></label>
        </div>
        
        <div class="row mt-4">
            <div class="col-6">
                <table class="table table-bordered kl-amount">
                    <tr>
                        <th>TOTAL</th>
                        <td><?= number_format($total_grand, 2) ?></td>
                    </tr>
                    <tr>
                        <th>PAID</th>
                        <td><?= number_format($total_grand, 2) ?></td>
                    </tr>
                    <tr>
                        <th>DUE</th>
                        <td>0.00</td>
                    </tr>
                </table>
            </div>
            <div class="col-6">
                <div class="kl-payment-by">
                    <p class="fw-bold mb-2">PAYMENT MADE BY</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="cash">
                        <label class="form-check-label" for="cash">CASH</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="cheque">
                        <label class="form-check-label" for="cheque">CHEQUE</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="eft">
                        <label class="form-check-label" for="eft">EFT</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="others">
                        <label class="form-check-label" for="others">OTHERS</label>
                    </div>
                    <div class="mt-2">
                        <label><strong>NOTES</strong> (If Any):</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4 row">
            <div class="col-7" style="font-size: 10px;">
                <p class="mt-1"><strong>Office Address:</strong> House# 460/A, Road# 6, Avenue# 7, Mirpur DOHS, Dhaka<br>
                <strong>Phone:</strong> 01552454543 <strong>Email:</strong> <EMAIL> <strong>Web:</strong> klay.tech</p>
            </div>
            <div class="col-5 text-end">
                <label class="fw-bold border-top mt-0 pt-2">AUTHORIZED SIGNATURE</label>
                <div>(Nasim Ahmed)</div>
            </div>
        </div>
    </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>

    </html>

<?php
} else {
    echo "No invoice ID provided.";
    exit;
}
?>

<?php //include 'includes/footer.php'; 
?>