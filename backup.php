<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Generate a secure backup filename
$backup_file = 'backup_' . date('Y-m-d_H-i-s') . '_' . bin2hex(random_bytes(8)) . '.sql';
$backup_path = __DIR__ . '/backups/' . $backup_file;

// Create backups directory if it doesn't exist
if (!is_dir(__DIR__ . '/backups')) {
    mkdir(__DIR__ . '/backups', 0755, true);
}

// Generate the backup file using PDO instead of exec
try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $backup_content = "";
    
    foreach ($tables as $table) {
        $backup_content .= "DROP TABLE IF EXISTS `$table`;\n";
        $create_table = $pdo->query("SHOW CREATE TABLE `$table`")->fetch(PDO::FETCH_ASSOC);
        $backup_content .= $create_table['Create Table'] . ";\n\n";
        
        $rows = $pdo->query("SELECT * FROM `$table`")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($rows as $row) {
            $values = array_map(function($value) use ($pdo) {
                return $value === null ? 'NULL' : $pdo->quote($value);
            }, $row);
            $backup_content .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
        }
        $backup_content .= "\n";
    }
    
    file_put_contents($backup_path, $backup_content);
    
    // Set proper file permissions
    chmod($backup_path, 0600);
    
    $page_title = "Database Backup";
    include 'includes/header.php';
    
    echo "<div class='alert alert-success'>Backup created successfully!</div>";
    echo "<a href='backups/{$backup_file}' class='btn btn-primary' download>Download Backup File</a>";
} catch (Exception $e) {
    $page_title = "Database Backup";
    include 'includes/header.php';
    echo "<div class='alert alert-danger'>Failed to create backup file: " . htmlspecialchars($e->getMessage()) . "</div>";
}

include 'includes/footer.php';
?>
