<?php
require_once 'auth.php';
$page_title = "Klay Invoice - Database Backup";
include 'includes/header.php';
include 'config.php';

// Generate the backup file
$backup_file = 'invoice_system_backup.sql';
exec("mysqldump -h " . $host . " -u " . $user . " -p" . $pass . " " . $db . " > " . $backup_file);

// Offer file for download
if(file_exists($backup_file)) {
    echo "<div class='alert alert-success'>Backup created successfully!</div>";
    echo "<a href='{$backup_file}' class='btn btn-primary' download>Download Backup File</a>";
} else {
    echo "<div class='alert alert-danger'>Failed to create backup file.</div>";
}

include 'includes/footer.php';
?>
