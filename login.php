<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// If already logged in, redirect to index
if(isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit();
}

// Include database connection
include 'config.php';

// Process login form
if(isset($_POST['login'])){
    $username = $_POST['username'];
    $password = $_POST['password'];

    // Debug information
    $debug_info = "Debug: Form submitted with username: " . htmlspecialchars($username) . "<br>";

    try {
        // Allow login with username or email
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();

        $debug_info .= "Debug: User found in database: " . ($user ? "Yes" : "No") . "<br>";

        if($user) {
            $debug_info .= "Debug: User ID: " . $user['id'] . "<br>";
            $debug_info .= "Debug: Email verified: " . ($user['email_verified'] ? "Yes" : "No") . "<br>";
            $debug_info .= "Debug: Password verification: " . (password_verify($password, $user['password']) ? "Success" : "Failed") . "<br>";
        }

        if($user && password_verify($password, $user['password'])){
            // Check if email is verified
            if(!$user['email_verified']) {
                $error = "Please verify your email address before logging in. Check your email for the verification link.";
                $show_resend_verification = true;
                $user_email = $user['email'];
            } else {
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['company_name'] = $user['company_name'];
                $_SESSION['email'] = $user['email'];

                $debug_info .= "Debug: Session variables set successfully<br>";

                // Redirect to index
                header("Location: index.php");
                exit();
            }
        } else {
            $error = "Invalid username/email or password";
        }
    } catch (Exception $e) {
        $error = "Database error: " . $e->getMessage();
        $debug_info .= "Debug: Exception: " . $e->getMessage() . "<br>";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Klay Invoice - Login</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <!-- Custom Professional CSS -->
    <link rel="stylesheet" href="assets/css/professional.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            margin: 2rem;
        }

        .login-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-body {
            padding: 2rem;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1rem 0.75rem;
            height: auto;
        }

        .form-floating .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-floating label {
            color: #64748b;
            font-weight: 500;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.875rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <!-- Floating Background Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container">
        <div class="login-header">
            <div class="logo-icon">
                <i class="bi bi-receipt-cutoff fs-2"></i>
            </div>
            <h2 class="mb-1 fw-bold">Klay Invoice</h2>
            <p class="mb-0 opacity-75">Professional Invoice Management</p>
        </div>

        <div class="login-body">
            <?php if(isset($error)): ?>
                <div class="alert alert-danger border-0 rounded-3 mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <span><?php echo $error; ?></span>
                    </div>
                    <?php if(isset($show_resend_verification) && $show_resend_verification): ?>
                        <div class="mt-2">
                            <a href="resend_verification.php?email=<?= urlencode($user_email) ?>" class="btn btn-sm btn-outline-danger">
                                <i class="bi bi-envelope me-1"></i>Resend Verification Email
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php if(isset($debug_info)): ?>
                <div class="alert alert-info border-0 rounded-3 mb-4" role="alert">
                    <small><?php echo $debug_info; ?></small>
                </div>
            <?php endif; ?>

            <form method="post" id="loginForm">
                <div class="form-floating mb-3">
                    <input type="text" class="form-control" id="username" name="username" placeholder="Username or Email" required>
                    <label for="username">
                        <i class="bi bi-person me-2"></i>Username or Email
                    </label>
                </div>

                <div class="form-floating mb-4">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                    <label for="password">
                        <i class="bi bi-lock me-2"></i>Password
                    </label>
                </div>

                <div class="d-grid mb-3">
                    <button type="submit" name="login" class="btn btn-primary btn-login btn-lg">
                        <span class="btn-text">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                        </span>
                        <span class="btn-loading d-none">
                            <span class="loading me-2"></span>Signing In...
                        </span>
                    </button>
                </div>

                <div class="text-center">
                    <p class="text-muted small mb-2">
                        Don't have an account?
                        <a href="register.php" class="text-decoration-none">Create one here</a>
                    </p>
                    <p class="text-muted small mb-2">
                        Forgot your password?
                        <a href="forgot_password.php" class="text-decoration-none">Reset it here</a>
                    </p>
                    <p class="text-muted small mb-0">
                        <i class="bi bi-shield-check me-1"></i>
                        Secure login powered by Klay Technologies
                    </p>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Simplified login form functionality - no interference with form submission
        document.addEventListener('DOMContentLoaded', function() {
            // Add focus effects to form inputs
            document.querySelectorAll('.form-control, .form-select').forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                    this.parentElement.style.transition = 'transform 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // Simple typing animation for the title
            const title = document.querySelector('.login-header h2');
            if (title) {
                const text = title.textContent;
                title.textContent = '';

                let i = 0;
                const typeWriter = () => {
                    if (i < text.length) {
                        title.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 100);
                    }
                };

                setTimeout(typeWriter, 500);
            }
        });
    </script>
</body>
</html>
