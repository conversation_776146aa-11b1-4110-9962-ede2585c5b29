<?php
// Start session
session_start();

// If already logged in, redirect to index
if(isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit();
}

// Include database connection and email functions
include 'config.php';
include 'email_config.php';

$message = '';
$message_type = '';
$show_form = true;

// Process forgot password form
if(isset($_POST['forgot_password'])){
    $email = trim($_POST['email']);

    // Validation
    if(empty($email)) {
        $message = "Email address is required";
        $message_type = "danger";
    } elseif(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = "Invalid email format";
        $message_type = "danger";
    } else {
        // Check if email exists
        $stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if($user) {
            // Generate password reset token
            $reset_token = generateSecureToken();
            $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour')); // Token expires in 1 hour
            
            // Store reset token in database
            $stmt = $pdo->prepare("INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = CURRENT_TIMESTAMP");
            $stmt->execute([$user['id'], $reset_token, $expires_at]);
            
            // Send password reset email
            if(sendPasswordResetEmail($user['email'], $user['username'], $reset_token)) {
                $message = "Password reset instructions have been sent to your email address. Please check your inbox and follow the instructions to reset your password.";
                $message_type = "success";
                $show_form = false;
            } else {
                $message = "Failed to send password reset email. Please try again later or contact support.";
                $message_type = "danger";
            }
        } else {
            // Don't reveal if email exists or not for security
            $message = "If an account with that email address exists, password reset instructions have been sent to your email.";
            $message_type = "info";
            $show_form = false;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Klay Invoice</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Custom Professional CSS -->
    <link rel="stylesheet" href="assets/css/professional.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }
        
        .forgot-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            margin: 2rem;
        }
        
        .forgot-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .forgot-body {
            padding: 2rem;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .btn-action {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.875rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="forgot-header">
            <div class="logo-icon">
                <i class="bi bi-key fs-2"></i>
            </div>
            <h2 class="mb-1 fw-bold">Forgot Password</h2>
            <p class="mb-0 opacity-75">Reset your account password</p>
        </div>
        
        <div class="forgot-body">
            <?php if(!empty($message)): ?>
                <div class="alert alert-<?= $message_type ?> border-0 rounded-3 mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <?php if($message_type === 'success'): ?>
                            <i class="bi bi-check-circle-fill me-2"></i>
                        <?php elseif($message_type === 'info'): ?>
                            <i class="bi bi-info-circle-fill me-2"></i>
                        <?php else: ?>
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php endif; ?>
                        <span><?= htmlspecialchars($message) ?></span>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if($show_form): ?>
                <p class="text-muted mb-4">
                    Enter your email address and we'll send you instructions to reset your password.
                </p>
                
                <form method="post">
                    <div class="form-floating mb-3">
                        <input type="email" class="form-control" id="email" name="email" placeholder="Email Address" required value="<?= isset($_POST['email']) ? htmlspecialchars($_POST['email']) : '' ?>">
                        <label for="email">
                            <i class="bi bi-envelope me-2"></i>Email Address
                        </label>
                    </div>

                    <div class="d-grid mb-3">
                        <button type="submit" name="forgot_password" class="btn btn-primary btn-action btn-lg">
                            <i class="bi bi-send me-2"></i>Send Reset Instructions
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <div class="text-center">
                    <div class="mb-4">
                        <i class="bi bi-envelope-check text-success" style="font-size: 3rem;"></i>
                    </div>
                    <p class="text-muted">
                        Check your email for password reset instructions.
                    </p>
                </div>
            <?php endif; ?>
            
            <div class="text-center">
                <p class="text-muted small mb-2">
                    Remember your password?
                    <a href="login.php" class="text-decoration-none">Back to Login</a>
                </p>
                <p class="text-muted small mb-2">
                    Don't have an account?
                    <a href="register.php" class="text-decoration-none">Create one here</a>
                </p>
                <p class="text-muted small mb-0">
                    <i class="bi bi-shield-check me-1"></i>
                    Secure password reset powered by Klay Technologies
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
