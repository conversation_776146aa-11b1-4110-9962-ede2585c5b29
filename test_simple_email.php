<?php
// Simple Email Test - No SMTP complications
require_once 'simple_email.php';

$message = '';
$message_type = '';
$system_info = getEmailSystemInfo();

if (isset($_POST['test_simple'])) {
    $test_email = trim($_POST['test_email']);
    
    if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        $message = "Please enter a valid email address.";
        $message_type = "danger";
    } else {
        $subject = "Simple Email Test - Klay Invoice System";
        $body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0;">✅ Email Test Successful!</h1>
                <p style="margin: 10px 0 0 0;">Klay Invoice System</p>
            </div>
            <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
                <h2 style="color: #333;">Simple Email System Working!</h2>
                <p>This email was sent using the simplified email system that avoids SMTP complications.</p>
                
                <h3 style="color: #555;">System Information:</h3>
                <ul style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #667eea;">
                    <li><strong>PHP Version:</strong> ' . phpversion() . '</li>
                    <li><strong>Mail Function:</strong> ' . (function_exists('mail') ? 'Available' : 'Not Available') . '</li>
                    <li><strong>OpenSSL:</strong> ' . (extension_loaded('openssl') ? 'Enabled' : 'Disabled') . '</li>
                    <li><strong>Method Used:</strong> Simple Email System</li>
                </ul>
                
                <p style="margin-top: 20px;">
                    <strong>Next Steps:</strong><br>
                    • Registration system is now working<br>
                    • Email verification will be saved as files<br>
                    • Perfect for development and testing
                </p>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="http://localhost:8000/register.php" style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Test Registration Now
                    </a>
                </div>
            </div>
        </div>';
        
        if (sendEmailSimple($test_email, $subject, $body, '<EMAIL>', 'Klay Invoice System')) {
            $message = "Email sent successfully! Check your inbox or the saved emails folder.";
            $message_type = "success";
        } else {
            $message = "Email sending failed, but this should not happen with the simple system.";
            $message_type = "warning";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Email Test - Klay Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            border-radius: 1rem 1rem 0 0 !important;
            padding: 2rem;
            text-align: center;
        }
        .system-info {
            background: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .status-good { color: #10b981; font-weight: 600; }
        .status-bad { color: #ef4444; font-weight: 600; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="card">
            <div class="card-header">
                <h2 class="mb-2"><i class="bi bi-envelope-check me-2"></i>Simple Email Test</h2>
                <p class="mb-0 opacity-75">No SMTP complications - Just works!</p>
            </div>
            
            <div class="card-body p-4">
                <?php if(!empty($message)): ?>
                    <div class="alert alert-<?= $message_type ?> border-0 rounded-3 mb-4" role="alert">
                        <i class="bi bi-<?= $message_type === 'success' ? 'check-circle' : 'exclamation-triangle' ?>-fill me-2"></i>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>
                
                <div class="system-info mb-4">
                    <h6 class="fw-bold mb-3"><i class="bi bi-info-circle me-2"></i>System Status</h6>
                    <div class="info-item">
                        <span>PHP Mail Function:</span>
                        <span class="<?= $system_info['mail_function'] ? 'status-good' : 'status-bad' ?>">
                            <?= $system_info['mail_function'] ? '✅ Available' : '❌ Not Available' ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span>OpenSSL Extension:</span>
                        <span class="<?= $system_info['openssl'] ? 'status-good' : 'status-bad' ?>">
                            <?= $system_info['openssl'] ? '✅ Enabled' : '❌ Disabled' ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span>PHP Version:</span>
                        <span class="status-good"><?= $system_info['php_version'] ?></span>
                    </div>
                    <div class="info-item">
                        <span>Email Method:</span>
                        <span class="status-good">Simple System (No SMTP)</span>
                    </div>
                </div>
                
                <form method="post">
                    <div class="mb-3">
                        <label for="test_email" class="form-label fw-bold">Test Email Address</label>
                        <input type="email" class="form-control form-control-lg" id="test_email" name="test_email" 
                               placeholder="Enter your email address" required 
                               value="<?= isset($_POST['test_email']) ? htmlspecialchars($_POST['test_email']) : '' ?>">
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            If mail() works, you'll receive an email. Otherwise, it will be saved as a file.
                        </div>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" name="test_simple" class="btn btn-primary btn-lg">
                            <i class="bi bi-send me-2"></i>Send Test Email
                        </button>
                    </div>
                </form>
                
                <div class="row g-2">
                    <div class="col-6">
                        <a href="view_emails.php" class="btn btn-outline-primary w-100">
                            <i class="bi bi-folder-open me-1"></i>View Saved Emails
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="register.php" class="btn btn-outline-success w-100">
                            <i class="bi bi-person-plus me-1"></i>Test Registration
                        </a>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="bi bi-shield-check me-1"></i>
                        Simple email system - No SMTP complications
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
