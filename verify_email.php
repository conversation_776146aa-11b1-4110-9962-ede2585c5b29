<?php
// Start session
session_start();

// Include database connection
include 'config.php';

$message = '';
$message_type = '';

// Check if token is provided
if(isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];
    
    // Find user with this verification token
    $stmt = $pdo->prepare("SELECT id, username, email, email_verified FROM users WHERE verification_token = ?");
    $stmt->execute([$token]);
    $user = $stmt->fetch();
    
    if($user) {
        if($user['email_verified']) {
            $message = "Your email is already verified. You can now login.";
            $message_type = 'info';
        } else {
            // Verify the email
            $stmt = $pdo->prepare("UPDATE users SET email_verified = 1, verification_token = NULL WHERE id = ?");
            if($stmt->execute([$user['id']])) {
                $message = "Email verified successfully! You can now login to your account.";
                $message_type = 'success';
            } else {
                $message = "Error verifying email. Please try again or contact support.";
                $message_type = 'danger';
            }
        }
    } else {
        $message = "Invalid or expired verification token. Please register again or contact support.";
        $message_type = 'danger';
    }
} else {
    $message = "No verification token provided.";
    $message_type = 'danger';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - Klay Invoice</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Custom Professional CSS -->
    <link rel="stylesheet" href="assets/css/professional.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }
        
        .verification-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            margin: 2rem;
        }
        
        .verification-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .verification-body {
            padding: 2rem;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .btn-action {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.875rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-header">
            <div class="logo-icon">
                <?php if($message_type === 'success'): ?>
                    <i class="bi bi-check-circle fs-2"></i>
                <?php elseif($message_type === 'info'): ?>
                    <i class="bi bi-info-circle fs-2"></i>
                <?php else: ?>
                    <i class="bi bi-exclamation-triangle fs-2"></i>
                <?php endif; ?>
            </div>
            <h2 class="mb-1 fw-bold">Email Verification</h2>
            <p class="mb-0 opacity-75">Klay Invoice System</p>
        </div>
        
        <div class="verification-body">
            <div class="alert alert-<?= $message_type ?> border-0 rounded-3 mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <?php if($message_type === 'success'): ?>
                        <i class="bi bi-check-circle-fill me-2"></i>
                    <?php elseif($message_type === 'info'): ?>
                        <i class="bi bi-info-circle-fill me-2"></i>
                    <?php else: ?>
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <?php endif; ?>
                    <span><?= htmlspecialchars($message) ?></span>
                </div>
            </div>
            
            <div class="text-center">
                <?php if($message_type === 'success' || $message_type === 'info'): ?>
                    <a href="login.php" class="btn btn-primary btn-action btn-lg">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Login Now
                    </a>
                <?php else: ?>
                    <a href="register.php" class="btn btn-primary btn-action btn-lg me-2">
                        <i class="bi bi-person-plus me-2"></i>Register Again
                    </a>
                    <a href="login.php" class="btn btn-outline-primary btn-lg">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Login
                    </a>
                <?php endif; ?>
            </div>
            
            <div class="text-center mt-4">
                <p class="text-muted small mb-0">
                    <i class="bi bi-shield-check me-1"></i>
                    Secure verification powered by Klay Technologies
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
