<?php
// Email Test Page
include 'email_config.php';

$message = '';
$message_type = '';

if (isset($_POST['send_test'])) {
    $test_email = trim($_POST['test_email']);
    
    if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        $message = "Please enter a valid email address.";
        $message_type = "danger";
    } else {
        // Send test email
        $subject = "Test Email from " . APP_NAME;
        $body = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Test Email</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>' . APP_NAME . '</h1>
                    <p>Email Configuration Test</p>
                </div>
                <div class="content">
                    <h2>Test Email Successful!</h2>
                    <p>If you received this email, your SMTP configuration is working correctly.</p>
                    <p><strong>Configuration Details:</strong></p>
                    <ul>
                        <li>SMTP Host: ' . SMTP_HOST . '</li>
                        <li>SMTP Port: ' . SMTP_PORT . '</li>
                        <li>From Email: ' . FROM_EMAIL . '</li>
                        <li>Encryption: ' . SMTP_ENCRYPTION . '</li>
                    </ul>
                    <p>You can now proceed with user registration and email verification.</p>
                    <p>Best regards,<br>The ' . APP_NAME . ' Team</p>
                </div>
            </div>
        </body>
        </html>';
        
        if (sendEmail($test_email, $subject, $body, true)) {
            $message = "Test email sent successfully to " . htmlspecialchars($test_email) . "! Please check your inbox.";
            $message_type = "success";
        } else {
            $message = "Failed to send test email. Please check your SMTP configuration and server logs.";
            $message_type = "danger";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Test - <?= APP_NAME ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            margin: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .test-body {
            padding: 2rem;
        }
        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <div class="logo-icon">
                <i class="bi bi-envelope-check fs-2"></i>
            </div>
            <h2 class="mb-1 fw-bold">Email Configuration Test</h2>
            <p class="mb-0 opacity-75"><?= APP_NAME ?></p>
        </div>
        
        <div class="test-body">
            <?php if(!empty($message)): ?>
                <div class="alert alert-<?= $message_type ?> border-0 rounded-3 mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-<?= $message_type === 'success' ? 'check-circle' : 'exclamation-triangle' ?>-fill me-2"></i>
                        <span><?= $message ?></span>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="mb-4">
                <h5>Current SMTP Configuration:</h5>
                <ul class="list-unstyled">
                    <li><strong>Host:</strong> <?= SMTP_HOST ?></li>
                    <li><strong>Port:</strong> <?= SMTP_PORT ?></li>
                    <li><strong>Username:</strong> <?= SMTP_USERNAME ?></li>
                    <li><strong>From Email:</strong> <?= FROM_EMAIL ?></li>
                    <li><strong>Encryption:</strong> <?= SMTP_ENCRYPTION ?></li>
                </ul>
            </div>
            
            <form method="post">
                <div class="mb-3">
                    <label for="test_email" class="form-label">Test Email Address</label>
                    <input type="email" class="form-control" id="test_email" name="test_email" 
                           placeholder="Enter email to test" required 
                           value="<?= isset($_POST['test_email']) ? htmlspecialchars($_POST['test_email']) : '' ?>">
                    <div class="form-text">We'll send a test email to this address to verify SMTP configuration.</div>
                </div>
                
                <div class="d-grid mb-3">
                    <button type="submit" name="send_test" class="btn btn-primary btn-lg">
                        <i class="bi bi-send me-2"></i>Send Test Email
                    </button>
                </div>
            </form>
            
            <div class="text-center">
                <p class="text-muted small mb-2">
                    <a href="register.php" class="text-decoration-none">Back to Registration</a> |
                    <a href="login.php" class="text-decoration-none">Login</a>
                </p>
                <p class="text-muted small mb-0">
                    <i class="bi bi-shield-check me-1"></i>
                    Email testing powered by <?= APP_NAME ?>
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
