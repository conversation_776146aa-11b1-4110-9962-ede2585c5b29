<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if invoice ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: invoices.php');
    exit;
}

$invoice_id = $_GET['id'];

// Verify invoice belongs to user
$stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = ? AND user_id = ?");
$stmt->execute([$invoice_id, $_SESSION['user_id']]);
$invoice = $stmt->fetch();

if (!$invoice) {
    header('Location: invoices.php');
    exit;
}

require_once 'auth.php';
require_once 'email_config.php';

$message = '';
$error = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $to = filter_input(INPUT_POST, 'to', FILTER_VALIDATE_EMAIL);
    $subject = htmlspecialchars($_POST['subject'] ?? '', ENT_QUOTES, 'UTF-8');
    $body = htmlspecialchars($_POST['body'] ?? '', ENT_QUOTES, 'UTF-8');
    $test_type = htmlspecialchars($_POST['test_type'] ?? '', ENT_QUOTES, 'UTF-8');

    if (!$to) {
        $error = 'Please enter a valid email address.';
    } elseif (!$subject && $test_type === 'custom') {
        $error = 'Please enter a subject.';
    } elseif (!$body && $test_type === 'custom') {
        $error = 'Please enter a message.';
    } else {
        switch ($test_type) {
            case 'verification':
                $token = generateSecureToken();
                $success = sendVerificationEmail($to, 'Test User', $token);
                break;
            case 'reset':
                $token = generateSecureToken();
                $success = sendPasswordResetEmail($to, 'Test User', $token);
                break;
            default:
                $success = sendEmail($to, $subject, $body);
        }

        if ($success) {
            $message = 'Test email sent successfully!';
        } else {
            $error = 'Failed to send test email. Check the error logs for details.';
        }
    }
}

// Get latest email info if available
$latestEmailInfo = '';
$latestEmailFile = __DIR__ . '/emails/latest_email.txt';
if (file_exists($latestEmailFile)) {
    $latestEmailInfo = file_get_contents($latestEmailFile);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .email-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .email-preview pre {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Test Email System</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success"><?php echo $message; ?></div>
                        <?php endif; ?>
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>

                        <form method="POST" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="to" class="form-label">To Email</label>
                                <input type="email" class="form-control" id="to" name="to" required>
                            </div>

                            <div class="mb-3">
                                <label for="test_type" class="form-label">Test Type</label>
                                <select class="form-select" id="test_type" name="test_type">
                                    <option value="custom">Custom Email</option>
                                    <option value="verification">Verification Email</option>
                                    <option value="reset">Password Reset Email</option>
                                </select>
                            </div>

                            <div class="mb-3 custom-email-fields">
                                <label for="subject" class="form-label">Subject</label>
                                <input type="text" class="form-control" id="subject" name="subject">
                            </div>

                            <div class="mb-3 custom-email-fields">
                                <label for="body" class="form-label">Message</label>
                                <textarea class="form-control" id="body" name="body" rows="5"></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">Send Test Email</button>
                        </form>

                        <?php if ($latestEmailInfo): ?>
                            <div class="email-preview mt-4">
                                <h5>Latest Email Info</h5>
                                <pre><?php echo htmlspecialchars($latestEmailInfo); ?></pre>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show/hide custom email fields based on test type
        document.getElementById('test_type').addEventListener('change', function() {
            const customFields = document.querySelectorAll('.custom-email-fields');
            customFields.forEach(field => {
                field.style.display = this.value === 'custom' ? 'block' : 'none';
            });
        });

        // Form validation
        (function() {
            'use strict';
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html> 