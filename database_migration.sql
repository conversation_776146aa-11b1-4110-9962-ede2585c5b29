-- Multi-Tenant Invoice System Database Migration
-- Run these SQL commands to upgrade the database

-- 1. First, let's enhance the users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS email VARCHAR(255) UNIQUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_name VA<PERSON><PERSON><PERSON>(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_address TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_phone VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_email VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_website VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_logo VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS verification_token VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- 2. Add user_id to all main tables to isolate data per user
ALTER TABLE clients ADD COLUMN IF NOT EXISTS user_id INT;
ALTER TABLE items ADD COLUMN IF NOT EXISTS user_id INT;
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS user_id INT;

-- 3. Add foreign key constraints
ALTER TABLE clients ADD CONSTRAINT fk_clients_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE items ADD CONSTRAINT fk_items_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE invoices ADD CONSTRAINT fk_invoices_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 4. For existing data, assign to user ID 1 (assuming there's an existing user)
UPDATE clients SET user_id = 1 WHERE user_id IS NULL;
UPDATE items SET user_id = 1 WHERE user_id IS NULL;
UPDATE invoices SET user_id = 1 WHERE user_id IS NULL;

-- 5. Make user_id NOT NULL after updating existing data
ALTER TABLE clients MODIFY user_id INT NOT NULL;
ALTER TABLE items MODIFY user_id INT NOT NULL;
ALTER TABLE invoices MODIFY user_id INT NOT NULL;

-- 6. Create email verification tokens table
CREATE TABLE IF NOT EXISTS email_verification_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_token (token)
);

-- 7. Create user settings table for customization
CREATE TABLE IF NOT EXISTS user_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_setting (user_id, setting_key)
);

-- 8. Insert default settings for existing users
INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) 
SELECT id, 'invoice_prefix', 'KL-INV-' FROM users;

INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) 
SELECT id, 'default_vat_rate', '5.00' FROM users;

INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) 
SELECT id, 'invoice_footer_text', 'Thank you for your business!' FROM users;

-- 9. Add indexes for better performance
CREATE INDEX idx_clients_user_id ON clients(user_id);
CREATE INDEX idx_items_user_id ON items(user_id);
CREATE INDEX idx_invoices_user_id ON invoices(user_id);
CREATE INDEX idx_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
