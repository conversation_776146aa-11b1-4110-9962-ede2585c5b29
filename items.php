<?php
require_once 'auth.php';
include 'config.php';

// Add Item BEFORE any output
if(isset($_POST['add_item'])){
    $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $summary = filter_input(INPUT_POST, 'summary', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $description = filter_input(INPUT_POST, 'description', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $price = filter_input(INPUT_POST, 'price', FILTER_VALIDATE_FLOAT);

    if($price === false || $price < 0) {
        $item_error = "Invalid price format!";
    } else {
        $stmt = $pdo->prepare("INSERT INTO items (name, summary, description, price) VALUES (?, ?, ?, ?)");
        $stmt->execute([$name, $summary, $description, $price]);
        $item_success = "Item added successfully!";
    }
}

// Set page title and include header AFTER form processing
$page_title = "Klay Invoice - Items";
include 'includes/header.php';

// Display success/error messages for add item
if(isset($item_error)) {
    echo "<div class='alert alert-danger text-center' role='alert'><i class='bi bi-exclamation-triangle-fill'></i> $item_error</div>";
}
if(isset($item_success)) {
    echo "<div class='alert alert-success text-center' role='alert'><i class='bi bi-check-circle-fill'></i> $item_success</div>";
}

// Handle deletion
if(isset($_POST['delete_item'])){
    try {
        $stmt = $pdo->prepare("DELETE FROM items WHERE id = ?");
        $stmt->execute([$_POST['delete_id']]);
        echo "<div class='alert alert-success text-center' role='alert'><i class='bi bi-check-circle-fill'></i> Item deleted successfully!</div>";
    } catch(PDOException $e) {
        echo "<div class='alert alert-danger text-center' role='alert'><i class='bi bi-exclamation-triangle-fill'></i> Cannot delete item that is used in invoices!</div>";
    }
}

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchCondition = '';
$params = [];

if (!empty($search)) {
    $searchCondition = " WHERE name LIKE :search OR summary LIKE :search OR description LIKE :search";
    $params[':search'] = "%$search%";
}

// Get Items with search
$stmt = $pdo->prepare("SELECT * FROM items $searchCondition ORDER BY name ASC");
if (!empty($params)) {
    foreach ($params as $param => $value) {
        $stmt->bindValue($param, $value);
    }
}
$stmt->execute();
$items = $stmt->fetchAll();
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-box-seam"></i> Items & Services</h2>
        </div>
    </div>

    <!-- Search Bar -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-search"></i> Search Items</h5>
        </div>
        <div class="card-body">
            <form method="get" class="mb-0">
                <div class="input-group">
                    <input type="text" name="search" class="form-control" placeholder="Search by name, summary or description..." 
                           value="<?= htmlspecialchars($search) ?>">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i> Search
                    </button>
                    <?php if(!empty($search)): ?>
                    <a href="items.php" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle"></i> Clear
                    </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Item Form -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-plus-circle"></i> Add New Item</h5>
        </div>
        <div class="card-body">
            <form method="post">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Item Name</label>
                            <input type="text" id="name" name="name" class="form-control" placeholder="Item Name" required>
                        </div>
                        <div class="mb-3">
                            <label for="summary" class="form-label">Summary</label>
                            <input type="text" id="summary" name="summary" class="form-control" placeholder="Brief summary">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="price" class="form-label">Price (BDT)</label>
                            <div class="input-group">
                                <input type="number" step="0.01" min="0" id="price" name="price" class="form-control" placeholder="0.00" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea id="description" name="description" class="form-control" placeholder="Detailed description" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="text-end">
                    <button type="submit" name="add_item" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Item
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Items Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0"><i class="bi bi-box-seam"></i> Item List</h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-primary"><?= count($items) ?> items found</span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th class="ps-3" width="5%">ID</th>
                            <th width="20%">Name</th>
                            <th width="15%">Summary</th>
                            <th width="40%">Description</th>
                            <th class="text-end" width="10%">Price (BDT)</th>
                            <th class="text-center" width="10%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(count($items) > 0): ?>
                            <?php foreach($items as $item): ?>
                            <tr>
                                <td class="ps-3"><?= $item['id'] ?></td>
                                <td><strong><?= htmlspecialchars($item['name']) ?></strong></td>
                                <td><?= htmlspecialchars($item['summary']) ?></td>
                                <td><?= htmlspecialchars($item['description']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($item['price'], 2) ?></td>
                                <td class="text-center">
                                    <div class="btn-group">
                                        <a href="edit_item.php?id=<?= $item['id'] ?>" class="btn btn-sm btn-warning" title="Edit Item">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $item['id'] ?>" title="Delete Item">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal<?= $item['id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $item['id'] ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel<?= $item['id'] ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body text-start">
                                                    <p>Are you sure you want to delete item: <strong><?= htmlspecialchars($item['name']) ?></strong>?</p>
                                                    <p class="text-danger"><strong>Warning:</strong> This action cannot be undone. If this item is used in any invoices, the deletion will fail.</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form method="post" style="display:inline;">
                                                        <input type="hidden" name="delete_id" value="<?= $item['id'] ?>">
                                                        <button type="submit" name="delete_item" class="btn btn-danger">Delete Item</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <i class="bi bi-info-circle text-info fs-4"></i><br>
                                    No items found. Add a new item using the form above.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
