<?php
// Database Migration Runner
// Run this file once to upgrade your database for multi-tenant support

include 'config.php';

echo "<h2>Klay Invoice System - Database Migration</h2>";
echo "<p>This will upgrade your database to support multi-tenant functionality.</p>";

try {
    // Read the migration SQL file
    $migration_sql = file_get_contents('database_migration.sql');
    
    if (!$migration_sql) {
        throw new Exception("Could not read database_migration.sql file");
    }
    
    // Split the SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $migration_sql)));
    
    echo "<h3>Running Migration...</h3>";
    echo "<ul>";
    
    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty statements and comments
        }
        
        try {
            $pdo->exec($statement);
            
            // Extract table/action info for display
            if (preg_match('/ALTER TABLE (\w+)/', $statement, $matches)) {
                echo "<li>✅ Modified table: {$matches[1]}</li>";
            } elseif (preg_match('/CREATE TABLE.*?(\w+)/', $statement, $matches)) {
                echo "<li>✅ Created table: {$matches[1]}</li>";
            } elseif (preg_match('/UPDATE (\w+)/', $statement, $matches)) {
                echo "<li>✅ Updated data in: {$matches[1]}</li>";
            } elseif (preg_match('/INSERT.*?INTO (\w+)/', $statement, $matches)) {
                echo "<li>✅ Inserted data into: {$matches[1]}</li>";
            } elseif (preg_match('/CREATE INDEX (\w+)/', $statement, $matches)) {
                echo "<li>✅ Created index: {$matches[1]}</li>";
            } else {
                echo "<li>✅ Executed SQL statement</li>";
            }
            
        } catch (PDOException $e) {
            // Some statements might fail if they already exist, that's okay
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate') !== false) {
                echo "<li>⚠️ Skipped (already exists): " . substr($statement, 0, 50) . "...</li>";
            } else {
                throw $e;
            }
        }
    }
    
    $pdo->commit();
    echo "</ul>";
    
    echo "<h3>✅ Migration Completed Successfully!</h3>";
    echo "<p><strong>What's New:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Enhanced user registration with company information</li>";
    echo "<li>✅ Email verification system</li>";
    echo "<li>✅ Multi-tenant data isolation (each user sees only their data)</li>";
    echo "<li>✅ User settings and customization</li>";
    echo "<li>✅ Improved security and data privacy</li>";
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Configure Email:</strong> Update SMTP settings in <code>email_config.php</code></li>";
    echo "<li><strong>Test Registration:</strong> Try creating a new account at <a href='register.php'>register.php</a></li>";
    echo "<li><strong>Verify Email:</strong> Check your email and click the verification link</li>";
    echo "<li><strong>Login:</strong> Use your new account to access the system</li>";
    echo "</ol>";
    
    echo "<h3>Important Notes:</h3>";
    echo "<ul>";
    echo "<li>🔒 <strong>Data Privacy:</strong> Each user now sees only their own invoices, clients, and items</li>";
    echo "<li>📧 <strong>Email Verification:</strong> New users must verify their email before logging in</li>";
    echo "<li>⚙️ <strong>Settings:</strong> Users can customize their invoice settings in Account Settings</li>";
    echo "<li>🏢 <strong>Company Info:</strong> Each user can set their company information for invoices</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php' class='btn btn-primary'>Go to Login Page</a></p>";
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    echo "<h3>❌ Migration Failed</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Migration - Klay Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .btn:hover { background: #0056b3; }
        code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; }
        ul, ol { margin: 10px 0; }
        li { margin: 5px 0; }
    </style>
</head>
<body>
</body>
</html>
