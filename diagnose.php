<?php
// Diagnostic page to check what's working and what's not
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>System Diagnostic</h1>";
echo "<hr>";

// Test 1: Check if email_config.php loads
echo "<h2>1. Testing email_config.php</h2>";
try {
    require_once 'email_config.php';
    echo "✅ email_config.php loaded successfully<br>";
    
    // Check if constants are defined
    $constants = ['FROM_EMAIL', 'FROM_NAME', 'APP_NAME', 'APP_URL'];
    foreach ($constants as $const) {
        if (defined($const)) {
            echo "✅ $const = " . constant($const) . "<br>";
        } else {
            echo "❌ $const not defined<br>";
        }
    }
    
    // Check if sendEmail function exists
    if (function_exists('sendEmail')) {
        echo "✅ sendEmail function exists<br>";
    } else {
        echo "❌ sendEmail function not found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error loading email_config.php: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Test 2: Check PHP mail function
echo "<h2>2. Testing PHP mail() function</h2>";
if (function_exists('mail')) {
    echo "✅ mail() function is available<br>";
    
    // Try to send a simple test email
    $test_result = mail('<EMAIL>', 'Test Subject', 'Test Body', 'From: <EMAIL>');
    if ($test_result) {
        echo "✅ mail() function returned true (but email may not actually send)<br>";
    } else {
        echo "❌ mail() function returned false<br>";
    }
} else {
    echo "❌ mail() function is not available<br>";
}

echo "<hr>";

// Test 3: Check file system permissions
echo "<h2>3. Testing file system</h2>";
$emails_dir = __DIR__ . '/emails';
if (is_dir($emails_dir)) {
    echo "✅ /emails directory exists<br>";
} else {
    echo "⚠️ /emails directory doesn't exist, trying to create...<br>";
    if (mkdir($emails_dir, 0755, true)) {
        echo "✅ Created /emails directory<br>";
    } else {
        echo "❌ Failed to create /emails directory<br>";
    }
}

if (is_writable($emails_dir)) {
    echo "✅ /emails directory is writable<br>";
} else {
    echo "❌ /emails directory is not writable<br>";
}

// Test file creation
$test_file = $emails_dir . '/test.txt';
if (file_put_contents($test_file, 'test')) {
    echo "✅ Can create files in /emails directory<br>";
    unlink($test_file); // Clean up
} else {
    echo "❌ Cannot create files in /emails directory<br>";
}

echo "<hr>";

// Test 4: Test the actual sendEmail function
echo "<h2>4. Testing sendEmail function</h2>";
if (function_exists('sendEmail')) {
    try {
        $result = sendEmail('<EMAIL>', 'Diagnostic Test', '<h1>Test Email</h1><p>This is a test.</p>');
        if ($result) {
            echo "✅ sendEmail function returned true<br>";
        } else {
            echo "❌ sendEmail function returned false<br>";
        }
    } catch (Exception $e) {
        echo "❌ sendEmail function threw exception: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ sendEmail function not available<br>";
}

echo "<hr>";

// Test 5: Check for saved emails
echo "<h2>5. Checking for saved emails</h2>";
if (is_dir($emails_dir)) {
    $email_files = glob($emails_dir . '/email_*.html');
    if (count($email_files) > 0) {
        echo "✅ Found " . count($email_files) . " saved email(s)<br>";
        foreach ($email_files as $file) {
            echo "- " . basename($file) . " (" . date('Y-m-d H:i:s', filemtime($file)) . ")<br>";
        }
    } else {
        echo "⚠️ No saved emails found<br>";
    }
} else {
    echo "❌ /emails directory not accessible<br>";
}

echo "<hr>";

// Test 6: Check database connection
echo "<h2>6. Testing database connection</h2>";
try {
    require_once 'config.php';
    echo "✅ config.php loaded successfully<br>";
    
    if (isset($pdo)) {
        echo "✅ PDO object exists<br>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT 1");
        if ($stmt) {
            echo "✅ Database connection working<br>";
        } else {
            echo "❌ Database query failed<br>";
        }
    } else {
        echo "❌ PDO object not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Test 7: Check registration page
echo "<h2>7. Testing registration page access</h2>";
if (file_exists('register.php')) {
    echo "✅ register.php file exists<br>";
    echo "<a href='register.php' target='_blank'>→ Open registration page</a><br>";
} else {
    echo "❌ register.php file not found<br>";
}

echo "<hr>";

// Test 8: System information
echo "<h2>8. System Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Operating System: " . php_uname() . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";

echo "<hr>";
echo "<h2>Quick Actions</h2>";
echo "<a href='register.php'>Test Registration</a> | ";
echo "<a href='view_emails.php'>View Emails</a> | ";
echo "<a href='test_email_final.php'>Test Email</a>";
?>
