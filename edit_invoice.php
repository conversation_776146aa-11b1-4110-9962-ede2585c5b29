
<?php
include 'config.php';

if (isset($_GET['id'])) {
    $invoice_id = $_GET['id'];
    $invoice = $pdo->query("SELECT * FROM invoices WHERE id = $invoice_id")->fetch();

    $client = $pdo->query("SELECT * FROM clients WHERE id = " . $invoice['client_id'])->fetch();

    $items = $pdo->query("SELECT * FROM invoice_items WHERE invoice_id = $invoice_id")->fetchAll();
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Edit Invoice</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
</head>
<body>
    <div class="container">
        <h1>Edit Invoice</h1>
        <form method="post" action="update_invoice.php">
            <input type="hidden" name="invoice_id" value="<?= $invoice_id ?>">
            <div class="form-group">
                <label for="client_id">Client:</label>
                <select name="client_id" id="client_id" class="form-select form-control">
                    <option value="">Select Client</option>
                    <?php foreach($clients as $client): ?>
                    <option value="<?= $client['id'] ?>" <?= $client['id'] == $invoice['client_id'] ? 'selected' : '' ?>><?= $client['name'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="form-group">
                <label for="invoice_date">Invoice Date:</label>
                <input type="date" name="invoice_date" id="invoice_date" class="form-control" value="<?= $invoice['invoice_date'] ?>">
            </div>
            <div class="form-group">
                <label for="due_date">Due Date:</label>
                <input type="date" name="due_date" id="due_date" class="form-control" value="<?= $invoice['due_date'] ?>">
            </div>
            <div id="items-container">
                <?php foreach($items as $item): ?>
                <div class="row mb-3 item-row">
                    <div class="col-md-4">
                        <select name="item_id[]" class="form-select form-control item-select">
                            <option value="">Select Item</option>
                            <?php foreach($items_list as $item_list): ?>
                            <option value="<?= $item_list['id'] ?>" <?= $item_list['id'] == $item['item_id'] ? 'selected' : '' ?>><?= $item_list['name'] ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="number" name="price_override[]" class="form-control" placeholder="Price Override" value="<?= $item['unit_price'] ?>">
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="special_notes[]" class="form-control" placeholder="Special Notes" value="<?= $item['special_notes'] ?>">
                    </div>
                    <div class="col-md-1">
                        <input type="number" name="quantity[]" class="form-control quantity" placeholder="Qty" min="1" value="<?= $item['quantity'] ?>">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger remove-row">Remove</button>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="form-group">
                <label for="vat_rate">VAT Rate:</label>
                <input type="number" name="vat_rate" id="vat_rate" class="form-control" value="<?= $invoice['vat_rate'] ?>">
            </div>
            <div class="form-group">
    <label for="discount_rate">Discount Rate:</label>
    <input type="number" name="discount_rate" id="discount_rate" class="form-control" value="<?= $invoice['discount_rate'] ?>">
</div>
<div class="form-group">
    <label for="discount_fixed">Discount Fixed:</label>
    <input type="number" name="discount_fixed" id="discount_fixed" class="form-control" value="<?= $invoice['discount_fixed'] ?>">
</div>
<div class="form-group">
    <label for="total">Total:</label>
    <input type="number" name="total" id="total" class="form-control" value="<?= $invoice['total'] ?>">
</div>
<button type="submit" class="btn btn-primary">Update Invoice</button>
</form>
</div>

<script>
    $(document).ready(function() {
        // Add new item row
        $('#add-row').click(function() {
            var newRow = $('.item-row').clone();
            newRow.find('input').val('');
            newRow.find('select').val('');
            $('#items-container').append(newRow);
        });

        // Remove item row
        $(document).on('click', '.remove-row', function() {
            $(this).closest('.item-row').remove();
        });
    });
</script>
</body>
</html>