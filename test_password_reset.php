<?php
// Test password reset functionality
require_once 'config.php';
require_once 'email_config.php';

echo "<h1>Password Reset Test</h1>";

// Test email
$test_email = '<EMAIL>';

echo "<h2>Step 1: Check if user exists</h2>";
$stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE email = ?");
$stmt->execute([$test_email]);
$user = $stmt->fetch();

if (!$user) {
    echo "<p>❌ User with email $test_email not found. Let's create one for testing.</p>";
    
    // Create test user
    $username = 'testuser';
    $password = password_hash('password123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, company_name, email_verified) VALUES (?, ?, ?, ?, 1)");
    if ($stmt->execute([$username, $test_email, $password, 'Test Company'])) {
        $user_id = $pdo->lastInsertId();
        echo "<p>✅ Created test user with ID: $user_id</p>";
        
        // Get the user data
        $stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
    } else {
        echo "<p>❌ Failed to create test user</p>";
        exit;
    }
} else {
    echo "<p>✅ User found: {$user['username']} ({$user['email']})</p>";
}

echo "<h2>Step 2: Generate password reset token</h2>";
$reset_token = generateSecureToken();
$expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

echo "<p><strong>Token:</strong> $reset_token</p>";
echo "<p><strong>Expires:</strong> $expires_at</p>";

// Store token in database
$stmt = $pdo->prepare("INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = CURRENT_TIMESTAMP");
if ($stmt->execute([$user['id'], $reset_token, $expires_at])) {
    echo "<p>✅ Token stored in database</p>";
} else {
    echo "<p>❌ Failed to store token</p>";
    exit;
}

echo "<h2>Step 3: Send password reset email</h2>";
if (sendPasswordResetEmail($user['email'], $user['username'], $reset_token)) {
    echo "<p>✅ Password reset email sent successfully</p>";
} else {
    echo "<p>❌ Failed to send password reset email</p>";
}

echo "<h2>Step 4: Test reset URL</h2>";
$reset_url = APP_URL . '/reset_password.php?token=' . $reset_token;
echo "<p><strong>Reset URL:</strong> <a href='$reset_url' target='_blank'>$reset_url</a></p>";

echo "<h2>Step 5: Verify token in database</h2>";
$stmt = $pdo->prepare("SELECT * FROM password_reset_tokens WHERE token = ? AND expires_at > NOW()");
$stmt->execute([$reset_token]);
$token_data = $stmt->fetch();

if ($token_data) {
    echo "<p>✅ Token is valid and not expired</p>";
    echo "<p><strong>User ID:</strong> {$token_data['user_id']}</p>";
    echo "<p><strong>Created:</strong> {$token_data['created_at']}</p>";
    echo "<p><strong>Expires:</strong> {$token_data['expires_at']}</p>";
} else {
    echo "<p>❌ Token not found or expired</p>";
}

echo "<hr>";
echo "<h2>Configuration Info:</h2>";
echo "<p><strong>APP_URL:</strong> " . APP_URL . "</p>";
echo "<p><strong>Current URL:</strong> http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "</p>";

echo "<hr>";
echo "<h2>Quick Actions:</h2>";
echo "<p><a href='$reset_url'>Test the Reset Link</a></p>";
echo "<p><a href='view_emails.php'>View Saved Emails</a></p>";
echo "<p><a href='debug_reset_tokens.php'>Debug All Tokens</a></p>";
echo "<p><a href='forgot_password.php'>Forgot Password Page</a></p>";
?>
