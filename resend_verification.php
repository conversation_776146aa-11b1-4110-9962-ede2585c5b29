<?php
// Start session and check authentication
session_start();
require_once 'auth.php';
require_once 'config.php';
require_once 'email_config.php';

// Check if user is already verified
$stmt = $pdo->prepare("SELECT email_verified, email, username FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if ($user['email_verified']) {
    $_SESSION['message'] = "Your email is already verified.";
    $_SESSION['message_type'] = "info";
    header("Location: settings.php");
    exit();
}

// Generate new verification token
$verification_token = generateSecureToken();

// Update user with new token
$stmt = $pdo->prepare("UPDATE users SET verification_token = ? WHERE id = ?");
$stmt->execute([$verification_token, $_SESSION['user_id']]);

// Send verification email
if (sendVerificationEmail($user['email'], $user['username'], $verification_token)) {
    $_SESSION['message'] = "Verification email sent successfully! Please check your email.";
    $_SESSION['message_type'] = "success";
} else {
    $_SESSION['message'] = "Failed to send verification email. Please try again later.";
    $_SESSION['message_type'] = "danger";
}

// Redirect back to settings
header("Location: settings.php");
exit();
?>
