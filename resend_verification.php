<?php
// Start session
session_start();
require_once 'config.php';
require_once 'email_config.php';

// Check if email parameter is provided (from login page)
$target_email = isset($_GET['email']) ? $_GET['email'] : null;
$user = null;

if ($target_email) {
    // Get user by email (for non-logged-in users)
    $stmt = $pdo->prepare("SELECT id, email_verified, email, username FROM users WHERE email = ?");
    $stmt->execute([$target_email]);
    $user = $stmt->fetch();
} elseif (isset($_SESSION['user_id'])) {
    // Get current logged-in user
    $stmt = $pdo->prepare("SELECT id, email_verified, email, username FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
} else {
    $_SESSION['message'] = "Invalid request. Please try logging in again.";
    $_SESSION['message_type'] = "danger";
    header("Location: login.php");
    exit();
}

if (!$user) {
    $_SESSION['message'] = "User not found.";
    $_SESSION['message_type'] = "danger";
    header("Location: login.php");
    exit();
}

if ($user['email_verified']) {
    $_SESSION['message'] = "Your email is already verified.";
    $_SESSION['message_type'] = "info";
    $redirect_url = isset($_SESSION['user_id']) ? "settings.php" : "login.php";
    header("Location: $redirect_url");
    exit();
}

// Generate new verification token
$verification_token = generateSecureToken();

// Update user with new token
$stmt = $pdo->prepare("UPDATE users SET verification_token = ? WHERE id = ?");
$stmt->execute([$verification_token, $user['id']]);

// Send verification email
if (sendVerificationEmail($user['email'], $user['username'], $verification_token)) {
    $_SESSION['message'] = "Verification email sent successfully! Please check your email and click the verification link.";
    $_SESSION['message_type'] = "success";
} else {
    $_SESSION['message'] = "Failed to send verification email. Please try again later.";
    $_SESSION['message_type'] = "danger";
}

// Redirect appropriately
$redirect_url = isset($_SESSION['user_id']) ? "settings.php" : "login.php";
header("Location: $redirect_url");
exit();
?>
