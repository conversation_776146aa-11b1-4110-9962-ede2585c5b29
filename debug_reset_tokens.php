<?php
// Debug password reset tokens
require_once 'config.php';

echo "<h1>Password Reset Tokens Debug</h1>";

try {
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'password_reset_tokens'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ password_reset_tokens table exists</p>";
        
        // Show all tokens
        $stmt = $pdo->query("SELECT prt.*, u.username, u.email FROM password_reset_tokens prt LEFT JOIN users u ON prt.user_id = u.id ORDER BY prt.created_at DESC");
        $tokens = $stmt->fetchAll();
        
        echo "<h2>All Password Reset Tokens:</h2>";
        if (count($tokens) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>User</th><th>Email</th><th>Token</th><th>Expires At</th><th>Created At</th><th>Status</th></tr>";
            
            foreach ($tokens as $token) {
                $is_expired = strtotime($token['expires_at']) < time();
                $status = $is_expired ? "❌ Expired" : "✅ Valid";
                $status_color = $is_expired ? "color: red;" : "color: green;";
                
                echo "<tr>";
                echo "<td>{$token['id']}</td>";
                echo "<td>{$token['username']}</td>";
                echo "<td>{$token['email']}</td>";
                echo "<td style='font-family: monospace; font-size: 12px;'>" . substr($token['token'], 0, 20) . "...</td>";
                echo "<td>{$token['expires_at']}</td>";
                echo "<td>{$token['created_at']}</td>";
                echo "<td style='$status_color'>$status</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check specific token from URL parameter or default
            $test_token = isset($_GET['token']) ? $_GET['token'] : '996b6f4b39611a796024ffbd209590e2760e19f98c75b8343088c143a9cbe7d0';
            echo "<h2>Testing Specific Token:</h2>";
            echo "<p><strong>Token:</strong> $test_token</p>";
            
            $stmt = $pdo->prepare("SELECT prt.*, u.username, u.email FROM password_reset_tokens prt LEFT JOIN users u ON prt.user_id = u.id WHERE prt.token = ?");
            $stmt->execute([$test_token]);
            $specific_token = $stmt->fetch();
            
            if ($specific_token) {
                echo "<p>✅ Token found in database</p>";
                echo "<p><strong>User:</strong> {$specific_token['username']} ({$specific_token['email']})</p>";
                echo "<p><strong>Expires:</strong> {$specific_token['expires_at']}</p>";
                echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
                
                if (strtotime($specific_token['expires_at']) < time()) {
                    echo "<p>❌ Token is expired</p>";
                } else {
                    echo "<p>✅ Token is still valid</p>";
                }
            } else {
                echo "<p>❌ Token not found in database</p>";
            }
            
        } else {
            echo "<p>No password reset tokens found</p>";
        }
        
        // Clean up expired tokens
        $stmt = $pdo->prepare("DELETE FROM password_reset_tokens WHERE expires_at < NOW()");
        $deleted = $stmt->execute();
        $count = $stmt->rowCount();
        echo "<p>🧹 Cleaned up $count expired tokens</p>";
        
    } else {
        echo "<p>❌ password_reset_tokens table does not exist</p>";
        echo "<p><a href='create_password_reset_table.php'>Create the table</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Configuration Check:</h2>";
echo "<p><strong>APP_URL:</strong> " . (defined('APP_URL') ? APP_URL : 'Not defined') . "</p>";
echo "<p><strong>Current URL:</strong> " . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) : 'Unknown') . "</p>";

echo "<hr>";
echo "<h2>Quick Actions:</h2>";
echo "<p><a href='forgot_password.php'>Test Forgot Password</a></p>";
echo "<p><a href='view_emails.php'>View Saved Emails</a></p>";
?>
