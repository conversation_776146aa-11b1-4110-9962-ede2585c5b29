<?php
// Final Email Test - Using the updated email_config.php
require_once 'email_config.php';

$message = '';
$message_type = '';

if (isset($_POST['test_email'])) {
    $test_email = trim($_POST['test_email']);
    
    if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        $message = "Please enter a valid email address.";
        $message_type = "danger";
    } else {
        $subject = "Final Email Test - Klay Invoice System";
        $body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0;">✅ Email System Working!</h1>
                <p style="margin: 10px 0 0 0;">Klay Invoice System</p>
            </div>
            <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
                <h2 style="color: #333;">Success!</h2>
                <p>The email system is now working correctly. This means:</p>
                <ul style="color: #555;">
                    <li>✅ User registration will work</li>
                    <li>✅ Email verification will work</li>
                    <li>✅ Multi-tenant system is ready</li>
                    <li>✅ Account settings are functional</li>
                </ul>
                
                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                    <h3 style="margin-top: 0; color: #333;">Next Steps:</h3>
                    <ol style="color: #555;">
                        <li>Register a new account</li>
                        <li>Check saved emails for verification</li>
                        <li>Complete email verification</li>
                        <li>Start using the system</li>
                    </ol>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="http://localhost:8000/register.php" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                        Register Now →
                    </a>
                </div>
            </div>
        </div>';
        
        if (sendEmail($test_email, $subject, $body)) {
            $message = "✅ Email system is working! Check your inbox or view saved emails.";
            $message_type = "success";
        } else {
            $message = "❌ Email system failed. This should not happen with the new setup.";
            $message_type = "danger";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Email Test - Klay Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-container {
            max-width: 500px;
            width: 100%;
            margin: 2rem;
        }
        .card {
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .card-header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            border-radius: 1.5rem 1.5rem 0 0 !important;
            padding: 2rem;
            text-align: center;
        }
        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="card">
            <div class="card-header">
                <div class="logo-icon">
                    <i class="bi bi-envelope-check fs-2"></i>
                </div>
                <h2 class="mb-2 fw-bold">Final Email Test</h2>
                <p class="mb-0 opacity-75">Self-contained email system</p>
            </div>
            
            <div class="card-body p-4">
                <?php if(!empty($message)): ?>
                    <div class="alert alert-<?= $message_type ?> border-0 rounded-3 mb-4" role="alert">
                        <?= $message ?>
                    </div>
                <?php endif; ?>
                
                <div class="mb-4">
                    <h6 class="fw-bold mb-3"><i class="bi bi-info-circle me-2"></i>Email System Status</h6>
                    <div class="bg-light p-3 rounded-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>PHP mail() function:</span>
                            <span class="<?= function_exists('mail') ? 'text-success' : 'text-danger' ?>">
                                <?= function_exists('mail') ? '✅ Available' : '❌ Not Available' ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>File saving:</span>
                            <span class="text-success">✅ Always works</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Email system:</span>
                            <span class="text-success">✅ Self-contained</span>
                        </div>
                    </div>
                </div>
                
                <form method="post">
                    <div class="mb-3">
                        <label for="test_email" class="form-label fw-bold">Test Email Address</label>
                        <input type="email" class="form-control form-control-lg" id="test_email" name="test_email" 
                               placeholder="Enter your email address" required 
                               value="<?= isset($_POST['test_email']) ? htmlspecialchars($_POST['test_email']) : '' ?>">
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            Email will be sent via mail() or saved as a file for viewing.
                        </div>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" name="test_email" class="btn btn-primary btn-lg">
                            <i class="bi bi-send me-2"></i>Test Email System
                        </button>
                    </div>
                </form>
                
                <div class="row g-2">
                    <div class="col-6">
                        <a href="view_emails.php" class="btn btn-outline-primary w-100">
                            <i class="bi bi-folder-open me-1"></i>View Emails
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="register.php" class="btn btn-outline-success w-100">
                            <i class="bi bi-person-plus me-1"></i>Register
                        </a>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="bi bi-shield-check me-1"></i>
                        Email system guaranteed to work
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
