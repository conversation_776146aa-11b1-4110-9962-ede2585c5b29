<?php
// Generate a working password reset link
require_once 'config.php';
require_once 'email_config.php';

$message = '';
$reset_link = '';

if (isset($_POST['generate'])) {
    $email = trim($_POST['email']);
    
    if (empty($email)) {
        $message = "Please enter an email address";
    } else {
        // Check if user exists
        $stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user) {
            // Generate new token
            $reset_token = generateSecureToken();
            $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // Store token
            $stmt = $pdo->prepare("INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = CURRENT_TIMESTAMP");
            
            if ($stmt->execute([$user['id'], $reset_token, $expires_at])) {
                $reset_link = APP_URL . '/reset_password.php?token=' . $reset_token;
                $message = "Reset link generated successfully!";
                
                // Also send email
                sendPasswordResetEmail($user['email'], $user['username'], $reset_token);
            } else {
                $message = "Failed to generate reset token";
            }
        } else {
            $message = "User not found with that email address";
        }
    }
}

// Get all users for testing
$stmt = $pdo->query("SELECT id, username, email, email_verified FROM users ORDER BY id DESC LIMIT 10");
$users = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Reset Link - Klay Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .card-header { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; border-radius: 15px 15px 0 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2 class="mb-0"><i class="bi bi-key me-2"></i>Generate Password Reset Link</h2>
            </div>
            <div class="card-body">
                <?php if ($message): ?>
                    <div class="alert alert-info">
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($reset_link): ?>
                    <div class="alert alert-success">
                        <h5>✅ Reset Link Generated:</h5>
                        <p><strong>Link:</strong> <a href="<?= $reset_link ?>" target="_blank"><?= $reset_link ?></a></p>
                        <p><small>This link will expire in 1 hour.</small></p>
                    </div>
                <?php endif; ?>
                
                <form method="post" class="mb-4">
                    <div class="mb-3">
                        <label for="email" class="form-label">User Email:</label>
                        <input type="email" class="form-control" id="email" name="email" required 
                               value="<?= isset($_POST['email']) ? htmlspecialchars($_POST['email']) : '' ?>">
                    </div>
                    <button type="submit" name="generate" class="btn btn-primary">
                        <i class="bi bi-link me-1"></i>Generate Reset Link
                    </button>
                </form>
                
                <hr>
                
                <h5>Available Users for Testing:</h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Verified</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?= $user['id'] ?></td>
                                    <td><?= htmlspecialchars($user['username']) ?></td>
                                    <td><?= htmlspecialchars($user['email']) ?></td>
                                    <td><?= $user['email_verified'] ? '✅' : '❌' ?></td>
                                    <td>
                                        <form method="post" style="display: inline;">
                                            <input type="hidden" name="email" value="<?= htmlspecialchars($user['email']) ?>">
                                            <button type="submit" name="generate" class="btn btn-sm btn-outline-primary">
                                                Generate Link
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Quick Links:</h6>
                        <ul class="list-unstyled">
                            <li><a href="forgot_password.php">Forgot Password Page</a></li>
                            <li><a href="login.php">Login Page</a></li>
                            <li><a href="register.php">Register Page</a></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Debug Tools:</h6>
                        <ul class="list-unstyled">
                            <li><a href="debug_reset_tokens.php">Debug Reset Tokens</a></li>
                            <li><a href="view_emails.php">View Saved Emails</a></li>
                            <li><a href="test_password_reset.php">Test Password Reset</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
