<?php
include 'config.php';

if (isset($_GET['id'])) {
    $client_id = $_GET['id'];
    $stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ? AND user_id = ?");
    $stmt->execute([$client_id, $_SESSION['user_id']]);
    $client = $stmt->fetch();

    if (!$client) {
        echo "Client not found or access denied.";
        exit;
    }
} else {
    echo "No client ID provided.";
    exit;
}

// Handle form submission BEFORE any output
if (isset($_POST['update_client'])) {
    $bin = filter_input(INPUT_POST, 'bin', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $address = filter_input(INPUT_POST, 'address', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

    $stmt = $pdo->prepare("UPDATE clients SET bin = ?, name = ?, email = ?, address = ? WHERE id = ? AND user_id = ?");
    $stmt->execute([$bin, $name, $email, $address, $client_id, $_SESSION['user_id']]);

    header('Location: clients.php');
    exit;
}

// Set page title and include header AFTER form processing
$page_title = "Klay Invoice - Edit Client";
include 'includes/header.php';
?>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">Edit Client</h5>
        <form method="post">
            <div class="form-group">
                <label for="bin">BIN:</label>
                <input type="text" id="bin" name="bin" value="<?= $client['bin'] ?>" class="form-control">
            </div>
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" id="name" name="name" value="<?= $client['name'] ?>" class="form-control">
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<?= $client['email'] ?>" class="form-control">
            </div>
            <div class="form-group">
                <label for="address">Address:</label>
                <input type="text" id="address" name="address" value="<?= $client['address'] ?>" class="form-control">
            </div>
            <br>
            <button type="submit" name="update_client" class="btn btn-primary">Update Client</button>
        </form>
    </div>
</div>

<?php include 'includes/footer.php'; ?>