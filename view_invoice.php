<?php
$page_title = "Invoice ID: KL-INV-" . $_GET['id'];
include 'includes/header.php'; 
include 'config.php';

//*** file uploads scripts ***
// Configuration
$upload_dir = 'uploads/';

// Check if the form has been submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['tds_vds_form'])) {
    // First get the invoice ID from the URL
    $invoice_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

    // Validate invoice ID exists
    $checkInvoice = $pdo->prepare("SELECT id FROM invoices WHERE id = ?");
    $checkInvoice->execute([$invoice_id]);
    if (!$checkInvoice->fetch()) {
        die('<div class="alert alert-danger text-center" role="alert">Invalid invoice ID</div>');
    }

    // Define the files to process
    $filesToProcess = [
        'tds_certificate',
        'tds_challan',
        'vds_certificate',
        'vds_challan',
        'wo_document'
    ];

    foreach ($filesToProcess as $fileKey) {
        if (!empty($_FILES[$fileKey]['name'])) {
            $file = $_FILES[$fileKey];
            
            if ($file['error'] !== UPLOAD_ERR_OK) {
                echo '<div class="alert alert-danger text-center" role="alert">Error uploading ' . $fileKey . ': ' . getUploadError($file['error']) . '</div>';
                continue;
            }

            // Validate file type and size here if needed

            // Generate a unique filename
            $filename = uniqid() . '_' . basename($file['name']);
            $destination = $upload_dir . $filename;

            if (move_uploaded_file($file['tmp_name'], $destination)) {
                echo '<div class="alert alert-success text-center" role="alert">' . $fileKey . ' uploaded successfully.</div>';

                // Update the database
                $columnMapping = [
                    'tds_certificate' => 'tds_certificate',
                    'tds_challan' => 'tds_challan',
                    'vds_certificate' => 'vds_certificate',
                    'vds_challan' => 'vds_challan',
                    'wo_document' => 'wo_document'
                ];

                if (isset($columnMapping[$fileKey])) {
                    $stmt = $pdo->prepare("UPDATE invoices 
                                          SET {$columnMapping[$fileKey]} = ?, 
                                              uploaded_at = NOW() 
                                          WHERE id = ?");
                    $stmt->execute([$destination, $invoice_id]);
                    
                    if ($stmt->rowCount() === 0) {
                        echo '<div class="alert alert-danger text-center" role="alert">Failed to update database for ' . $fileKey . '</div>';
                    }
                } else {
                    echo '<div class="alert alert-danger text-center" role="alert">Invalid file type: ' . $fileKey . '</div>';
                }
            } else {
                echo '<div class="alert alert-danger text-center" role="alert">Failed to move ' . $fileKey . '.</div>';
            }
        }
    }
}

// Function to get the upload error message
function getUploadError($error) {
    switch ($error) {
        case UPLOAD_ERR_INI_SIZE:
            return 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
        case UPLOAD_ERR_FORM_SIZE:
            return 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
        case UPLOAD_ERR_PARTIAL:
            return 'The uploaded file was only partially uploaded';
        case UPLOAD_ERR_NO_FILE:
            return 'No file was uploaded';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Missing a temporary folder';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Failed to write file to disk';
        case UPLOAD_ERR_EXTENSION:
            return 'A PHP extension stopped the file upload';
        default:
            return 'Unknown upload error';
    }
}
//*** file uploads scripts ends ***

if (isset($_GET['id'])) {
    $invoice_id = $_GET['id'];
    $invoice = $pdo->query("SELECT * FROM invoices 
                           JOIN clients ON invoices.client_id = clients.id
                           WHERE invoices.id = $invoice_id")->fetch();

    $items = $pdo->query("SELECT * FROM invoice_items 
                         JOIN items ON invoice_items.item_id = items.id
                         WHERE invoice_id = $invoice_id")->fetchAll();

    // Calculate Totals
    $subtotal = 0;
    foreach($items as $item){
        $subtotal += $item['unit_price'] * $item['quantity'];
    }

    $discount_rate = $subtotal * ($invoice['discount_rate'] / 100);
    $discount_fixed = $invoice['discount_fixed'];
    $total = $subtotal - ($discount_rate + $discount_fixed);
    $tax = $total * ($invoice['vat_rate'] / 100);
    $total_grand = $total + $tax;

    $invoice_for = $invoice['invoice_for'];
    $service_type = $invoice['service_type'];

    // Format dates with null check
    $invoice_date = !empty($invoice['invoice_date']) ? date("d M Y", strtotime($invoice['invoice_date'])) : 'Not Set';
    $quote_date = !empty($invoice['quote_date']) ? date("d M Y", strtotime($invoice['quote_date'])) : 'Not Set';
    $invoice_id_date = !empty($invoice['quote_date']) ? date("ymd", strtotime($invoice['quote_date'])) : date("ymd");
?>

<div id="my-pdf-content" class="card">
    <div class="card-body">
        <h6>Invoice ID: KL-INV-<?= $invoice_id_date ?>-<?= $invoice_id ?></h6>
        <h5 class="mb-0">Invoice Title: <?= $invoice_for ?></h5>
        <h6>Service Type: <?= $service_type ?></h6>
        <br>
        <div class="row mb-4">
            <div class="col-md-6">
                <p class="mb-0"><strong>Client:</strong> <?= $invoice['name'] ?></p>
                <p class="mb-0"><strong>Email:</strong> <?= $invoice['email'] ?></p>
                <p class="mb-0"><strong>Address:</strong> <?= $invoice['address'] ?></p>
            </div>
            <div class="col-md-6 text-end">
                <p class="mb-0"><strong>Quotation Date:</strong> <?= $quote_date ?></p>
                <p class="mb-0"><strong>Invoice Date:</strong> <?= $invoice_date ?></p>
                <p class="mb-0"><strong>Status:</strong> <?= ucfirst($invoice['payment_status']) ?></p>
            </div>
        </div>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Description</th>
                    <th class="text-end">Unit Price</th>
                    <th class="text-center">Qty</th>
                    <th class="text-end">Price (BDT)</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach($items as $item): ?>
                <tr>
                    <td><?= $item['name'] ?></td>
                    <td><?= $item['summary'] ?></td>
                    <td class="text-end"><?= number_format($item['unit_price'], 2) ?></td>
                    <td class="text-center"><?= $item['quantity'] ?></td>
                    <td class="text-end"><?= number_format($item['unit_price'] * $item['quantity'], 2) ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="row">
            <div class="col-md-4 offset-md-8">
                <table class="table text-end">
                    <tr>
                        <th>Subtotal</th>
                        <td><?= number_format($subtotal, 2) ?></td>
                    </tr>

                    <?php if ($invoice['discount_rate'] > 0 || $invoice['discount_fixed'] > 0): ?>
                        <tr>
                            <th>Discount
                                <?php if ($invoice['discount_rate'] > 0): ?>
                                    (<?= $invoice['discount_rate'] ?>%)
                                <?php else: ?>
                                    (Onetime)
                                <?php endif; ?>
                                <div style="font-weight: normal; font-size: 13px; font-style: italic;"><?= $invoice['discount_notes'] ?></div>
                            </th>
                            <td>
                                <?php if ($invoice['discount_rate'] > 0): ?>
                                    -<?= number_format($discount_rate, 2) ?>
                                <?php else: ?>
                                    -<?= number_format($invoice['discount_fixed'], 2) ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Total</th>
                            <td><?= number_format($total, 2) ?></td>
                        </tr>
                    <?php endif; ?>

                    <tr>
                        <th>VAT (<?= $invoice['vat_rate'] ?>%)</th>
                        <td><?= number_format($tax, 2) ?></td>
                    </tr>
                    <tr>
                        <th>Grand Total</th>
                        <td><?= number_format($total_grand, 2) ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
} else {
    echo "No invoice ID provided.";
    exit;
}
?>
<br>
<hr>
<br>
<form method="post" class="mb-4">
    <div class="row">
        <div class="col-md-3">
            <input type="number" step="0.01" name="amount" class="form-control" placeholder="Amount" required>
        </div>
        <div class="col-md-3">
            <input type="date" name="payment_date" class="form-control" required>
        </div>
        <div class="col-md-3">
            <select name="payment_method" class="form-select form-control" required>
                <option value="Cash">Cash</option>
                <option value="Credit Card">Credit Card</option>
                <option value="Bank Transfer">Bank Transfer</option>
            </select>
        </div>
        <div class="col-md-3">
            <button type="submit" name="add_payment" class="btn btn-primary">Add Payment</button>
        </div>
    </div>
</form>

<hr>

<!-- <a href="pdf_export.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-warning">Download PDF</a> -->
<!-- <a href="pdf_export.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-warning">Download PDF (with Signature)</a> -->

<a href="view_pdf_new.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2" target="_blank">Invoice</a>
<a href="view_pdf_new_sign.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2" target="_blank">Invoice (/w Signature)</a>
<a href="view_pdf_quote.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2" target="_blank">Quotation</a>
<a href="view_pdf_quote_vat.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2" target="_blank">Quotation (/w VAT)</a>
<a href="view_challan.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2" target="_blank">Challan</a>
<a href="view_receipt.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2" target="_blank">Receipt</a>
<a href="assets/download/tax_exempt.pdf" class="btn btn-lg btn-primary mb-2" target="_blank">TAX Exempt Certificate</a>
<a href="edit_invoice_new.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-danger mb-2">Edit</a>

<hr>
<!-- <h3>TDS & VDS</h3> -->
<form method="post" enctype="multipart/form-data">
    <input type="hidden" name="tds_vds_form" value="1">
    <div class="row">
        <div class="row mt-3">
            <div class="col-md-3">
                <label for="wo_document">Work Order (WO):</label>
                <input type="file" name="wo_document" id="wo_document" class="form-control">
            </div>
        </div>
    </div>
    <hr>
    <div class="row">
        <div class="col-md-3">
            <label for="tds_certificate">TDS Certificate:</label>
            <input type="file" name="tds_certificate" id="tds_certificate" class="form-control">
        </div>
        <div class="col-md-3">
            <label for="tds_challan">TDS Challan:</label>
            <input type="file" name="tds_challan" id="tds_challan" class="form-control">
        </div>
        <div class="col-md-3">
            <label for="vds_certificate">VDS Certificate:</label>
            <input type="file" name="vds_certificate" id="vds_certificate" class="form-control">
        </div>
        <div class="col-md-3">
            <label for="vds_challan">VDS Challan:</label>
            <input type="file" name="vds_challan" id="vds_challan" class="form-control">
        </div>
    </div>
    <br>
    <button type="submit" class="btn btn-primary">Submit</button>
</form>
<hr>
<h3>Download WO</h3>
<div class="row">
    <?php if (!empty($invoice['wo_document'])): ?>
    <div class="col-md-3">
        <label>Work Order:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['wo_document'] ?>">Download</a>
    </div>
    <?php endif; ?>
</div>
<hr>
<h3>Download TDS & VDS</h3>
<div class="row">
    <?php if (!empty($invoice['tds_certificate'])): ?>
    <div class="col-md-3">
        <label>TDS Certificate:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['tds_certificate'] ?>">Download</a>
    </div>
    <?php endif; ?>
    <?php if (!empty($invoice['tds_challan'])): ?>
    <div class="col-md-3">
        <label>TDS Challan:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['tds_challan'] ?>">Download</a>
    </div>
    <?php endif; ?>
    <?php if (!empty($invoice['vds_certificate'])): ?>
    <div class="col-md-3">
        <label>VDS Certificate:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['vds_certificate'] ?>">Download</a>
    </div>
    <?php endif; ?>
    <?php if (!empty($invoice['vds_challan'])): ?>
    <div class="col-md-3">
        <label>VDS Challan:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['vds_challan'] ?>">Download</a>
    </div>
    <?php endif; ?>
</div>

<hr>

<?php include 'includes/footer.php'; ?>
