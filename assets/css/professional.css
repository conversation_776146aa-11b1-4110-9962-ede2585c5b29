/* Professional Invoice System Styles */

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --border-radius: 0.75rem;
    --border-radius-lg: 1rem;
}

/* Global Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
}

/* Enhanced Navigation */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    box-shadow: var(--shadow-lg);
    border-bottom: 3px solid var(--primary-light);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand:hover {
    color: #e0e7ff !important;
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white !important;
}

/* Fix avatar and dropdown alignment */
.navbar-nav .dropdown-toggle {
    display: flex !important;
    align-items: center !important;
}

.navbar-nav .dropdown-toggle .rounded-circle {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dropdown Enhancements */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-xl);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.dropdown-item {
    border-radius: calc(var(--border-radius) - 0.25rem);
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white !important;
    transform: translateX(4px);
}

.dropdown-item.text-danger:hover {
    background-color: var(--danger-color) !important;
    color: white !important;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    background: white;
}

.card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
    padding: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Dashboard Stats Cards */
.stats-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    border-top: none !important;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stats-card.success {
    border-left-color: var(--success-color);
}

.stats-card.warning {
    border-left-color: var(--warning-color);
}

.stats-card.danger {
    border-left-color: var(--danger-color);
}

/* Fix text overflow in stats cards */
.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h2 {
    font-size: 1.4rem;
    line-height: 1.2;
    word-break: break-word;
    overflow-wrap: break-word;
}

.stats-card h6 {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

/* Button Enhancements */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #047857 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #b45309 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #b91c1c 100%);
}

/* Table Enhancements */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}
.table-no-border {
    border-radius: 0 !important;
}

.table thead th {
    background: linear-gradient(135deg, var(--dark-color) 0%, #334155 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f1f5f9;
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    border-color: var(--border-color);
    vertical-align: middle;
}

/* Badge Enhancements */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Form Enhancements */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background-color: white;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background-color: white;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: #ecfdf5;
    color: #065f46;
    border-left-color: var(--success-color);
}

.alert-danger {
    background-color: #fef2f2;
    color: #991b1b;
    border-left-color: var(--danger-color);
}

.alert-warning {
    background-color: #fffbeb;
    color: #92400e;
    border-left-color: var(--warning-color);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.shadow-custom {
    box-shadow: var(--shadow-xl);
}

/* Additional Professional Enhancements */

/* Breadcrumb Styling */
.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--secondary-color);
    font-weight: 600;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--secondary-color);
    font-weight: 500;
}

/* Enhanced Card Headers */
.card-header.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    border: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
}

/* Fix table header radius when inside cards */
.card .table thead th:first-child {
    border-top-left-radius: 0 !important;
}

.card .table thead th:last-child {
    border-top-right-radius: 0 !important;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: #f8fafc;
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table thead th {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e2e8f0;
}

/* Badge Enhancements */
.badge.bg-opacity-10 {
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
}

/* Card Footer */
.card-footer {
    background-color: #f8fafc !important;
    border-top: 1px solid #e2e8f0;
    padding: 1rem 1.5rem;
}



/* Dashboard Header */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Stats Cards Animation */
.stats-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Icon Containers */
.bg-primary.bg-opacity-10 {
    background-color: rgba(37, 99, 235, 0.1) !important;
}

.bg-success.bg-opacity-10 {
    background-color: rgba(5, 150, 105, 0.1) !important;
}

.bg-warning.bg-opacity-10 {
    background-color: rgba(217, 119, 6, 0.1) !important;
}

.bg-danger.bg-opacity-10 {
    background-color: rgba(220, 38, 38, 0.1) !important;
}

.bg-info.bg-opacity-10 {
    background-color: rgba(6, 182, 212, 0.1) !important;
}

/* Table date display fixes */
.table .small {
    white-space: nowrap;
    font-size: 0.8rem;
}

.table td {
    vertical-align: middle;
}

/* Simple Footer Styling */
footer {
    background-color: #f8fafc !important;
    border-top: 1px solid #e2e8f0 !important;
}

footer .text-muted {
    color: #64748b !important;
}

footer .text-dark {
    color: #1e293b !important;
}

footer a:hover {
    color: var(--primary-color) !important;
    text-decoration: underline !important;
}

/* Responsive Design Improvements */
@media (max-width: 992px) {
    .stats-card {
        margin-bottom: 1.5rem;
    }

    .card-body {
        padding: 1.25rem;
    }
}

@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem !important;
    }

    .stats-card .card-body {
        padding: 1rem;
    }

    .stats-card h2 {
        font-size: 1.5rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .breadcrumb {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .stats-card h2 {
        font-size: 1.25rem;
    }

    .stats-card .card-body {
        padding: 0.875rem;
    }
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Smooth Transitions */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* Focus States */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .navbar,
    .breadcrumb,
    footer {
        display: none !important;
    }

    .container {
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
