<?php
/**
 * Simple, reliable email system that avoids SMTP issues
 * Focuses on methods that work reliably in development
 */

/**
 * Send email using the most reliable method available
 */
function sendEmailSimple($to, $subject, $body, $fromEmail = '<EMAIL>', $fromName = 'Klay Invoice System') {
    error_log("Simple email send attempt to: $to, Subject: $subject");
    
    // Method 1: Try basic mail() function first (often works on local dev)
    if (tryBasicMail($to, $subject, $body, $fromEmail, $fromName)) {
        error_log("Basic mail() succeeded for: $to");
        return true;
    }
    
    // Method 2: Try sendmail directly (if available)
    if (trySendmail($to, $subject, $body, $fromEmail, $fromName)) {
        error_log("Sendmail succeeded for: $to");
        return true;
    }
    
    // Method 3: Save to file (always works, perfect for development)
    error_log("All email methods failed, saving to file for: $to");
    return saveEmailToFileSimple($to, $subject, $body, $fromEmail, $fromName);
}

/**
 * Try basic PHP mail() function with enhanced headers
 */
function tryBasicMail($to, $subject, $body, $fromEmail, $fromName) {
    try {
        // Enhanced headers for better delivery
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . $fromName . ' <' . $fromEmail . '>',
            'Reply-To: ' . $fromEmail,
            'Return-Path: ' . $fromEmail,
            'X-Mailer: PHP/' . phpversion(),
            'X-Priority: 3',
            'Date: ' . date('r'),
            'Message-ID: <' . uniqid() . '@localhost>'
        ];
        
        $result = mail($to, $subject, $body, implode("\r\n", $headers));
        
        if ($result) {
            error_log("mail() function returned true");
            return true;
        } else {
            error_log("mail() function returned false");
            return false;
        }
    } catch (Exception $e) {
        error_log("mail() function exception: " . $e->getMessage());
        return false;
    }
}

/**
 * Try sendmail directly (Unix/Linux systems)
 */
function trySendmail($to, $subject, $body, $fromEmail, $fromName) {
    // Check if sendmail is available
    if (!file_exists('/usr/sbin/sendmail') && !file_exists('/usr/bin/sendmail')) {
        return false;
    }
    
    try {
        $sendmail_path = file_exists('/usr/sbin/sendmail') ? '/usr/sbin/sendmail' : '/usr/bin/sendmail';
        
        $headers = "From: $fromName <$fromEmail>\r\n";
        $headers .= "Reply-To: $fromEmail\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
        $headers .= "Subject: $subject\r\n";
        $headers .= "\r\n";
        
        $message = $headers . $body;
        
        $handle = popen("$sendmail_path -t", 'w');
        if ($handle) {
            fwrite($handle, "To: $to\r\n");
            fwrite($handle, $message);
            $result = pclose($handle);
            
            if ($result === 0) {
                error_log("Sendmail succeeded");
                return true;
            }
        }
        
        error_log("Sendmail failed");
        return false;
    } catch (Exception $e) {
        error_log("Sendmail exception: " . $e->getMessage());
        return false;
    }
}

/**
 * Save email to file with enhanced formatting
 */
function saveEmailToFileSimple($to, $subject, $body, $fromEmail, $fromName) {
    try {
        // Create emails directory
        $emailDir = __DIR__ . '/emails';
        if (!is_dir($emailDir)) {
            mkdir($emailDir, 0755, true);
        }
        
        // Generate filename
        $timestamp = date('Y-m-d_H-i-s');
        $filename = $emailDir . "/email_{$timestamp}_" . uniqid() . '.html';
        
        // Create enhanced email preview
        $emailContent = createEmailPreview($to, $subject, $body, $fromEmail, $fromName, $filename);
        
        // Save to file
        if (file_put_contents($filename, $emailContent)) {
            // Update latest email pointer
            $latestFile = $emailDir . '/latest_email.txt';
            $latestInfo = "Latest Email Saved\n";
            $latestInfo .= "===================\n";
            $latestInfo .= "File: " . basename($filename) . "\n";
            $latestInfo .= "To: $to\n";
            $latestInfo .= "Subject: $subject\n";
            $latestInfo .= "Time: " . date('Y-m-d H:i:s') . "\n";
            $latestInfo .= "View: http://localhost:8000/view_emails.php?view=" . urlencode(basename($filename)) . "\n";
            
            file_put_contents($latestFile, $latestInfo);
            
            error_log("Email saved successfully to: $filename");
            return true;
        } else {
            error_log("Failed to save email to file");
            return false;
        }
    } catch (Exception $e) {
        error_log("File save exception: " . $e->getMessage());
        return false;
    }
}

/**
 * Create enhanced email preview HTML
 */
function createEmailPreview($to, $subject, $body, $fromEmail, $fromName, $filename) {
    $timestamp = date('Y-m-d H:i:s');
    $basename = basename($filename);
    
    return "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Email Preview: $subject</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .email-container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 12px; 
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .email-header { 
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .email-meta { 
            background: #f8fafc; 
            padding: 20px; 
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
        }
        .email-body { 
            padding: 30px; 
            line-height: 1.6; 
        }
        .meta-row { 
            display: flex; 
            margin-bottom: 8px; 
        }
        .meta-label { 
            font-weight: 600; 
            width: 100px; 
            color: #475569; 
        }
        .meta-value { 
            color: #1e293b; 
        }
        .status-badge {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }
        .view-link {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 6px;
            margin-top: 10px;
            font-size: 14px;
        }
        .view-link:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div class='email-container'>
        <div class='email-header'>
            <h1 style='margin: 0; font-size: 24px;'>📧 Email Preview</h1>
            <p style='margin: 10px 0 0 0; opacity: 0.9;'>Development Mode - Klay Invoice System</p>
        </div>
        
        <div class='email-meta'>
            <div class='meta-row'>
                <div class='meta-label'>To:</div>
                <div class='meta-value'>$to</div>
            </div>
            <div class='meta-row'>
                <div class='meta-label'>From:</div>
                <div class='meta-value'>$fromName &lt;$fromEmail&gt;</div>
            </div>
            <div class='meta-row'>
                <div class='meta-label'>Subject:</div>
                <div class='meta-value'>$subject</div>
            </div>
            <div class='meta-row'>
                <div class='meta-label'>Date:</div>
                <div class='meta-value'>$timestamp</div>
            </div>
            <div class='meta-row'>
                <div class='meta-label'>File:</div>
                <div class='meta-value'>$basename <span class='status-badge'>SAVED</span></div>
            </div>
            <div class='meta-row'>
                <div class='meta-label'>Status:</div>
                <div class='meta-value'>
                    Email saved for development preview
                    <a href='http://localhost:8000/view_emails.php' class='view-link'>View All Emails</a>
                </div>
            </div>
        </div>
        
        <div class='email-body'>
            $body
        </div>
    </div>
</body>
</html>";
}

/**
 * Get system email configuration info
 */
function getEmailSystemInfo() {
    return [
        'mail_function' => function_exists('mail'),
        'sendmail_path' => ini_get('sendmail_path'),
        'smtp_host' => ini_get('SMTP'),
        'smtp_port' => ini_get('smtp_port'),
        'openssl' => extension_loaded('openssl'),
        'curl' => extension_loaded('curl'),
        'php_version' => phpversion()
    ];
}
?>
