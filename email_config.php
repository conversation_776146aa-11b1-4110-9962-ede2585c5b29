<?php
// Clean Email Configuration - Guaranteed to work

// Include PHPMailer
require_once __DIR__ . '/vendor/autoload.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

// SMTP Configuration
define('SMTP_HOST', 'smtp-relay.brevo.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'xsmtpsib-a3fc978fae52179fe014c22faba0a8e428bee7b5953f339bdf47347191524291-sf9Ap0tdSLacIKxb');
define('SMTP_ENCRYPTION', 'tls');

// Email Settings
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Klay Invoice');
define('REPLY_TO_EMAIL', '<EMAIL>');

// Application Settings
define('APP_NAME', 'Klay Invoice');
define('APP_URL', 'http://localhost/playground/ai/invoice-system');
define('SUPPORT_EMAIL', '<EMAIL>');

/**
 * Send email using PHPMailer
 */
function sendEmail($to, $subject, $body, $isHTML = true) {
    // Method 1: Try PHPMailer
    if (tryPHPMailer($to, $subject, $body, $isHTML)) {
        return true;
    }

    // Method 2: Try PHP mail() function
    if (tryPhpMail($to, $subject, $body)) {
        return true;
    }

    // Method 3: Save to file (always works)
    return saveEmailToFile($to, $subject, $body);
}

/**
 * Try sending email using PHPMailer
 */
function tryPHPMailer($to, $subject, $body, $isHTML = true) {
    try {
        $mail = new PHPMailer(true);

        // Server settings
        $mail->SMTPDebug = SMTP::DEBUG_OFF;
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION;
        $mail->Port = SMTP_PORT;

        // Recipients
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress($to);
        $mail->addReplyTo(REPLY_TO_EMAIL, FROM_NAME);

        // Content
        $mail->isHTML($isHTML);
        $mail->Subject = $subject;
        $mail->Body = $body;
        if (!$isHTML) {
            $mail->AltBody = strip_tags($body);
        }

        $mail->send();
        return true;
    } catch (Exception) {
        return false;
    }
}

function tryPhpMail($to, $subject, $body) {
    try {
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
            'Reply-To: ' . FROM_EMAIL,
            'X-Mailer: PHP/' . phpversion(),
            'Date: ' . date('r')
        ];

        return mail($to, $subject, $body, implode("\r\n", $headers));
    } catch (Exception) {
        return false;
    }
}

function saveEmailToFile($to, $subject, $body) {
    try {
        // Create emails directory
        $emailDir = __DIR__ . '/emails';
        if (!is_dir($emailDir)) {
            mkdir($emailDir, 0755, true);
        }

        // Create filename
        $filename = $emailDir . '/email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.html';

        // Create email content
        $emailContent = createEmailHTML($to, $subject, $body, $filename);

        // Save file
        if (file_put_contents($filename, $emailContent)) {
            // Update latest email info
            $latestFile = $emailDir . '/latest_email.txt';
            $info = "Latest Email: " . basename($filename) . "\n";
            $info .= "To: $to\n";
            $info .= "Subject: $subject\n";
            $info .= "Time: " . date('Y-m-d H:i:s') . "\n";
            $info .= "View: " . APP_URL . "/view_emails.php?view=" . urlencode(basename($filename));
            file_put_contents($latestFile, $info);

            return true;
        }
    } catch (Exception) {
        // File save failed
    }

    return false;
}

function createEmailHTML($to, $subject, $body, $filename) {
    $timestamp = date('Y-m-d H:i:s');
    $basename = basename($filename);

    return "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>$subject</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .meta { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; }
        .content { padding: 30px; }
        .meta-item { margin-bottom: 10px; }
        .label { font-weight: bold; color: #495057; }
        .value { color: #212529; }
        .badge { background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>📧 Email Preview</h1>
            <p>Klay Invoice System - Development Mode</p>
        </div>
        <div class='meta'>
            <div class='meta-item'><span class='label'>To:</span> <span class='value'>$to</span></div>
            <div class='meta-item'><span class='label'>Subject:</span> <span class='value'>$subject</span></div>
            <div class='meta-item'><span class='label'>Date:</span> <span class='value'>$timestamp</span></div>
            <div class='meta-item'><span class='label'>File:</span> <span class='value'>$basename</span> <span class='badge'>SAVED</span></div>
        </div>
        <div class='content'>
            $body
        </div>
    </div>
</body>
</html>";
}

function sendVerificationEmail($email, $full_name, $token) {
    $subject = '🎉 Welcome to ' . APP_NAME . ' - Verify Your Email';
    $verification_url = APP_URL . '/verify_email.php?token=' . $token;

    $body = '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background: #f8fafc;
                padding: 20px;
            }
            .email-wrapper { max-width: 600px; margin: 0 auto; background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 40px 30px;
                text-align: center;
                position: relative;
            }
            .header::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url("data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.1\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
            }
            .header h1 { font-size: 28px; font-weight: 700; margin-bottom: 8px; position: relative; z-index: 1; }
            .header p { font-size: 16px; opacity: 0.9; position: relative; z-index: 1; }
            .content { padding: 40px 30px; }
            .greeting { font-size: 24px; font-weight: 600; color: #1a202c; margin-bottom: 20px; }
            .message { font-size: 16px; color: #4a5568; margin-bottom: 30px; line-height: 1.7; }
            .cta-container { text-align: center; margin: 40px 0; }
            .button {
                display: inline-block;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 16px 32px;
                text-decoration: none;
                border-radius: 50px;
                font-weight: 600;
                font-size: 16px;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                transition: all 0.3s ease;
            }
            .button:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6); }
            .link-fallback {
                background: #f7fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 20px;
                margin: 30px 0;
                font-size: 14px;
                color: #718096;
            }
            .link-fallback a { color: #667eea; word-break: break-all; }
            .warning {
                background: #fef5e7;
                border-left: 4px solid #f6ad55;
                padding: 16px;
                border-radius: 0 8px 8px 0;
                margin: 30px 0;
                font-size: 14px;
                color: #744210;
            }
            .footer {
                background: #f8fafc;
                padding: 30px;
                text-align: center;
                border-top: 1px solid #e2e8f0;
                color: #718096;
                font-size: 14px;
            }
            .footer a { color: #667eea; text-decoration: none; }
            .social-links { margin: 20px 0; }
            .social-links a {
                display: inline-block;
                margin: 0 10px;
                color: #a0aec0;
                font-size: 20px;
                text-decoration: none;
            }
            @media (max-width: 600px) {
                .email-wrapper { margin: 10px; border-radius: 12px; }
                .header { padding: 30px 20px; }
                .content { padding: 30px 20px; }
                .greeting { font-size: 20px; }
                .button { padding: 14px 28px; font-size: 15px; }
            }
        </style>
    </head>
    <body>
        <div class="email-wrapper">
            <div class="header">
                <h1>🎉 Welcome to ' . APP_NAME . '!</h1>
                <p>Your Professional Invoice Management System</p>
            </div>
            <div class="content">
                <div class="greeting">Hello ' . htmlspecialchars($full_name) . '! 👋</div>

                <div class="message">
                    Thank you for joining <strong>' . APP_NAME . '</strong>! We\'re excited to help you streamline your invoice management and grow your business.
                </div>

                <div class="message">
                    To get started and access all the powerful features, please verify your email address by clicking the button below:
                </div>

                <div class="cta-container">
                    <a href="' . $verification_url . '" class="button">✅ Verify Email Address</a>
                </div>

                <div class="link-fallback">
                    <strong>Button not working?</strong> Copy and paste this link into your browser:<br>
                    <a href="' . $verification_url . '">' . $verification_url . '</a>
                </div>

                <div class="warning">
                    ⏰ <strong>Important:</strong> This verification link will expire in 24 hours for security reasons.
                </div>

                <div class="message">
                    Once verified, you\'ll be able to:
                    <ul style="margin: 15px 0; padding-left: 20px; color: #4a5568;">
                        <li>Create and send professional invoices</li>
                        <li>Manage clients and track payments</li>
                        <li>Generate detailed reports</li>
                        <li>Customize your invoice templates</li>
                    </ul>
                </div>

                <div class="message">
                    If you didn\'t create this account, you can safely ignore this email.
                </div>

                <div style="margin-top: 40px; padding-top: 30px; border-top: 1px solid #e2e8f0; color: #718096;">
                    Best regards,<br>
                    <strong>The ' . APP_NAME . ' Team</strong> 🚀
                </div>
            </div>
            <div class="footer">
                <div class="social-links">
                    <a href="#" title="Twitter">🐦</a>
                    <a href="#" title="LinkedIn">💼</a>
                    <a href="#" title="Facebook">📘</a>
                </div>
                <p>© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</p>
                <p>Need help? Contact us at <a href="mailto:' . SUPPORT_EMAIL . '">' . SUPPORT_EMAIL . '</a></p>
                <p style="margin-top: 15px; font-size: 12px; color: #a0aec0;">
                    You received this email because you signed up for ' . APP_NAME . '.
                </p>
            </div>
        </div>
    </body>
    </html>';

    return sendEmail($email, $subject, $body, true);
}

function sendPasswordResetEmail($email, $full_name, $token) {
    $subject = '🔐 Password Reset Request - ' . APP_NAME;
    $reset_url = APP_URL . '/reset_password.php?token=' . $token;

    $body = '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background: #f8fafc;
                padding: 20px;
            }
            .email-wrapper { max-width: 600px; margin: 0 auto; background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
            .header {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                padding: 40px 30px;
                text-align: center;
                position: relative;
            }
            .header::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url("data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.1\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
            }
            .header h1 { font-size: 28px; font-weight: 700; margin-bottom: 8px; position: relative; z-index: 1; }
            .header p { font-size: 16px; opacity: 0.9; position: relative; z-index: 1; }
            .content { padding: 40px 30px; }
            .greeting { font-size: 24px; font-weight: 600; color: #1a202c; margin-bottom: 20px; }
            .message { font-size: 16px; color: #4a5568; margin-bottom: 30px; line-height: 1.7; }
            .cta-container { text-align: center; margin: 40px 0; }
            .button {
                display: inline-block;
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                padding: 16px 32px;
                text-decoration: none;
                border-radius: 50px;
                font-weight: 600;
                font-size: 16px;
                box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
                transition: all 0.3s ease;
            }
            .button:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6); }
            .link-fallback {
                background: #fef2f2;
                border: 1px solid #fecaca;
                border-radius: 8px;
                padding: 20px;
                margin: 30px 0;
                font-size: 14px;
                color: #991b1b;
            }
            .link-fallback a { color: #dc2626; word-break: break-all; }
            .warning {
                background: #fef3cd;
                border-left: 4px solid #f59e0b;
                padding: 16px;
                border-radius: 0 8px 8px 0;
                margin: 30px 0;
                font-size: 14px;
                color: #92400e;
            }
            .security-notice {
                background: #f0f9ff;
                border-left: 4px solid #0ea5e9;
                padding: 16px;
                border-radius: 0 8px 8px 0;
                margin: 30px 0;
                font-size: 14px;
                color: #0c4a6e;
            }
            .footer {
                background: #f8fafc;
                padding: 30px;
                text-align: center;
                border-top: 1px solid #e2e8f0;
                color: #718096;
                font-size: 14px;
            }
            .footer a { color: #667eea; text-decoration: none; }
            @media (max-width: 600px) {
                .email-wrapper { margin: 10px; border-radius: 12px; }
                .header { padding: 30px 20px; }
                .content { padding: 30px 20px; }
                .greeting { font-size: 20px; }
                .button { padding: 14px 28px; font-size: 15px; }
            }
        </style>
    </head>
    <body>
        <div class="email-wrapper">
            <div class="header">
                <h1>🔐 Password Reset</h1>
                <p>Secure Password Reset for ' . APP_NAME . '</p>
            </div>
            <div class="content">
                <div class="greeting">Hello ' . htmlspecialchars($full_name) . '! 👋</div>

                <div class="message">
                    We received a request to reset your password for your <strong>' . APP_NAME . '</strong> account.
                </div>

                <div class="message">
                    If you requested this password reset, click the button below to create a new password:
                </div>

                <div class="cta-container">
                    <a href="' . $reset_url . '" class="button">🔑 Reset My Password</a>
                </div>

                <div class="link-fallback">
                    <strong>Button not working?</strong> Copy and paste this link into your browser:<br>
                    <a href="' . $reset_url . '">' . $reset_url . '</a>
                </div>

                <div class="warning">
                    ⏰ <strong>Important:</strong> This reset link will expire in 1 hour for security reasons.
                </div>

                <div class="security-notice">
                    🛡️ <strong>Security Notice:</strong> If you didn\'t request this password reset, please ignore this email. Your password will remain unchanged and your account is secure.
                </div>

                <div class="message">
                    For your security, we recommend:
                    <ul style="margin: 15px 0; padding-left: 20px; color: #4a5568;">
                        <li>Using a strong, unique password</li>
                        <li>Not sharing your password with anyone</li>
                        <li>Logging out from shared devices</li>
                        <li>Contacting us if you notice suspicious activity</li>
                    </ul>
                </div>

                <div style="margin-top: 40px; padding-top: 30px; border-top: 1px solid #e2e8f0; color: #718096;">
                    Best regards,<br>
                    <strong>The ' . APP_NAME . ' Security Team</strong> 🔒
                </div>
            </div>
            <div class="footer">
                <p>© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</p>
                <p>Need help? Contact us at <a href="mailto:' . SUPPORT_EMAIL . '">' . SUPPORT_EMAIL . '</a></p>
                <p style="margin-top: 15px; font-size: 12px; color: #a0aec0;">
                    This email was sent because a password reset was requested for your account.
                </p>
            </div>
        </div>
    </body>
    </html>';

    return sendEmail($email, $subject, $body, true);
}

function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}
?>
