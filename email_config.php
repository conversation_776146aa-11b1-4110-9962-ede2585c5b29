<?php
// Clean Email Configuration - Guaranteed to work

// Include PHPMailer
require_once __DIR__ . '/vendor/autoload.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

// SMTP Configuration
define('SMTP_HOST', 'smtp-relay.brevo.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'xsmtpsib-a3fc978fae52179fe014c22faba0a8e428bee7b5953f339bdf47347191524291-sf9Ap0tdSLacIKxb');
define('SMTP_ENCRYPTION', 'tls');

// Email Settings
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Klay Invoice');
define('REPLY_TO_EMAIL', '<EMAIL>');

// Application Settings
define('APP_NAME', 'Klay Invoice');
define('APP_URL', 'http://localhost/playground/ai/invoice-system');
define('SUPPORT_EMAIL', '<EMAIL>');

/**
 * Send email using PHPMailer
 */
function sendEmail($to, $subject, $body, $isHTML = true) {
    // Method 1: Try PHPMailer
    if (tryPHPMailer($to, $subject, $body, $isHTML)) {
        return true;
    }

    // Method 2: Try PHP mail() function
    if (tryPhpMail($to, $subject, $body)) {
        return true;
    }

    // Method 3: Save to file (always works)
    return saveEmailToFile($to, $subject, $body);
}

/**
 * Try sending email using PHPMailer
 */
function tryPHPMailer($to, $subject, $body, $isHTML = true) {
    try {
        $mail = new PHPMailer(true);

        // Server settings
        $mail->SMTPDebug = SMTP::DEBUG_OFF;
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION;
        $mail->Port = SMTP_PORT;

        // Recipients
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress($to);
        $mail->addReplyTo(REPLY_TO_EMAIL, FROM_NAME);

        // Content
        $mail->isHTML($isHTML);
        $mail->Subject = $subject;
        $mail->Body = $body;
        if (!$isHTML) {
            $mail->AltBody = strip_tags($body);
        }

        $mail->send();
        return true;
    } catch (Exception) {
        return false;
    }
}

function tryPhpMail($to, $subject, $body) {
    try {
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
            'Reply-To: ' . FROM_EMAIL,
            'X-Mailer: PHP/' . phpversion(),
            'Date: ' . date('r')
        ];

        return mail($to, $subject, $body, implode("\r\n", $headers));
    } catch (Exception) {
        return false;
    }
}

function saveEmailToFile($to, $subject, $body) {
    try {
        // Create emails directory
        $emailDir = __DIR__ . '/emails';
        if (!is_dir($emailDir)) {
            mkdir($emailDir, 0755, true);
        }

        // Create filename
        $filename = $emailDir . '/email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.html';

        // Create email content
        $emailContent = createEmailHTML($to, $subject, $body, $filename);

        // Save file
        if (file_put_contents($filename, $emailContent)) {
            // Update latest email info
            $latestFile = $emailDir . '/latest_email.txt';
            $info = "Latest Email: " . basename($filename) . "\n";
            $info .= "To: $to\n";
            $info .= "Subject: $subject\n";
            $info .= "Time: " . date('Y-m-d H:i:s') . "\n";
            $info .= "View: " . APP_URL . "/view_emails.php?view=" . urlencode(basename($filename));
            file_put_contents($latestFile, $info);

            return true;
        }
    } catch (Exception) {
        // File save failed
    }

    return false;
}

function createEmailHTML($to, $subject, $body, $filename) {
    $timestamp = date('Y-m-d H:i:s');
    $basename = basename($filename);

    return "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>$subject</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .meta { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; }
        .content { padding: 30px; }
        .meta-item { margin-bottom: 10px; }
        .label { font-weight: bold; color: #495057; }
        .value { color: #212529; }
        .badge { background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>📧 Email Preview</h1>
            <p>Klay Invoice System - Development Mode</p>
        </div>
        <div class='meta'>
            <div class='meta-item'><span class='label'>To:</span> <span class='value'>$to</span></div>
            <div class='meta-item'><span class='label'>Subject:</span> <span class='value'>$subject</span></div>
            <div class='meta-item'><span class='label'>Date:</span> <span class='value'>$timestamp</span></div>
            <div class='meta-item'><span class='label'>File:</span> <span class='value'>$basename</span> <span class='badge'>SAVED</span></div>
        </div>
        <div class='content'>
            $body
        </div>
    </div>
</body>
</html>";
}

function sendVerificationEmail($email, $username, $token) {
    $subject = 'Verify Your Email - ' . APP_NAME;
    $verification_url = APP_URL . '/verify_email.php?token=' . $token;
    
    $body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Email Verification</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . APP_NAME . '</h1>
                <p>Welcome to Your Invoice Management System</p>
            </div>
            <div class="content">
                <h2>Hello ' . htmlspecialchars($username) . '!</h2>
                <p>Thank you for registering with ' . APP_NAME . '. To complete your registration and start using your invoice management system, please verify your email address.</p>
                
                <p><a href="' . $verification_url . '" class="button">Verify Email Address</a></p>
                
                <p>If the button above doesn\'t work, you can copy and paste this link into your browser:</p>
                <p><a href="' . $verification_url . '">' . $verification_url . '</a></p>
                
                <p><strong>This verification link will expire in 24 hours.</strong></p>
                
                <p>If you didn\'t create an account with us, please ignore this email.</p>
                
                <p>Best regards,<br>The ' . APP_NAME . ' Team</p>
            </div>
            <div class="footer">
                <p>© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</p>
                <p>If you have any questions, contact us at <a href="mailto:' . SUPPORT_EMAIL . '">' . SUPPORT_EMAIL . '</a></p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail($email, $subject, $body, true);
}

function sendPasswordResetEmail($email, $username, $token) {
    $subject = 'Password Reset Request - ' . APP_NAME;
    $reset_url = APP_URL . '/reset_password.php?token=' . $token;
    
    $body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Password Reset</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . APP_NAME . '</h1>
                <p>Password Reset Request</p>
            </div>
            <div class="content">
                <h2>Hello ' . htmlspecialchars($username) . '!</h2>
                <p>We received a request to reset your password for your ' . APP_NAME . ' account.</p>
                
                <p><a href="' . $reset_url . '" class="button">Reset Password</a></p>
                
                <p>If the button above doesn\'t work, you can copy and paste this link into your browser:</p>
                <p><a href="' . $reset_url . '">' . $reset_url . '</a></p>
                
                <p><strong>This reset link will expire in 1 hour.</strong></p>
                
                <p>If you didn\'t request a password reset, please ignore this email. Your password will remain unchanged.</p>
                
                <p>Best regards,<br>The ' . APP_NAME . ' Team</p>
            </div>
            <div class="footer">
                <p>If you have any questions, contact us at <a href="mailto:' . SUPPORT_EMAIL . '">' . SUPPORT_EMAIL . '</a></p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail($email, $subject, $body, true);
}

function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}
?>
