<?php
// Email Configuration for SMTP
// Update these settings with your SMTP provider details

// SMTP Configuration
define('SMTP_HOST', 'smtp.gmail.com'); // Change to your SMTP host
define('SMTP_PORT', 587); // Usually 587 for TLS, 465 for SSL
define('SMTP_USERNAME', '<EMAIL>'); // Your email
define('SMTP_PASSWORD', 'your-app-password'); // Your email password or app password
define('SMTP_ENCRYPTION', 'tls'); // 'tls' or 'ssl'

// Email Settings
define('FROM_EMAIL', '<EMAIL>'); // From email address
define('FROM_NAME', 'Klay Invoice System'); // From name
define('REPLY_TO_EMAIL', '<EMAIL>'); // Reply-to email

// Application Settings
define('APP_NAME', 'Klay Invoice System');
define('APP_URL', 'http://localhost:8000'); // Change to your domain
define('SUPPORT_EMAIL', '<EMAIL>');

/**
 * Send email using SMTP
 */
function sendEmail($to, $subject, $body, $isHTML = true) {
    // Simple SMTP implementation using PHP's mail() function with headers
    // For production, consider using PHPMailer or SwiftMailer
    
    $headers = array();
    $headers[] = 'MIME-Version: 1.0';
    $headers[] = 'Content-type: ' . ($isHTML ? 'text/html' : 'text/plain') . '; charset=UTF-8';
    $headers[] = 'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>';
    $headers[] = 'Reply-To: ' . REPLY_TO_EMAIL;
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    
    return mail($to, $subject, $body, implode("\r\n", $headers));
}

/**
 * Send verification email
 */
function sendVerificationEmail($email, $username, $token) {
    $subject = 'Verify Your Email - ' . APP_NAME;
    $verification_url = APP_URL . '/verify_email.php?token=' . $token;
    
    $body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Email Verification</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . APP_NAME . '</h1>
                <p>Welcome to Your Invoice Management System</p>
            </div>
            <div class="content">
                <h2>Hello ' . htmlspecialchars($username) . '!</h2>
                <p>Thank you for registering with ' . APP_NAME . '. To complete your registration and start using your invoice management system, please verify your email address.</p>
                
                <p><a href="' . $verification_url . '" class="button">Verify Email Address</a></p>
                
                <p>If the button above doesn\'t work, you can copy and paste this link into your browser:</p>
                <p><a href="' . $verification_url . '">' . $verification_url . '</a></p>
                
                <p><strong>This verification link will expire in 24 hours.</strong></p>
                
                <p>If you didn\'t create an account with us, please ignore this email.</p>
                
                <p>Best regards,<br>The ' . APP_NAME . ' Team</p>
            </div>
            <div class="footer">
                <p>© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</p>
                <p>If you have any questions, contact us at <a href="mailto:' . SUPPORT_EMAIL . '">' . SUPPORT_EMAIL . '</a></p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail($email, $subject, $body, true);
}

/**
 * Send password reset email
 */
function sendPasswordResetEmail($email, $username, $token) {
    $subject = 'Password Reset Request - ' . APP_NAME;
    $reset_url = APP_URL . '/reset_password.php?token=' . $token;
    
    $body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Password Reset</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . APP_NAME . '</h1>
                <p>Password Reset Request</p>
            </div>
            <div class="content">
                <h2>Hello ' . htmlspecialchars($username) . '!</h2>
                <p>We received a request to reset your password for your ' . APP_NAME . ' account.</p>
                
                <p><a href="' . $reset_url . '" class="button">Reset Password</a></p>
                
                <p>If the button above doesn\'t work, you can copy and paste this link into your browser:</p>
                <p><a href="' . $reset_url . '">' . $reset_url . '</a></p>
                
                <p><strong>This reset link will expire in 1 hour.</strong></p>
                
                <p>If you didn\'t request a password reset, please ignore this email. Your password will remain unchanged.</p>
                
                <p>Best regards,<br>The ' . APP_NAME . ' Team</p>
            </div>
            <div class="footer">
                <p>© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</p>
                <p>If you have any questions, contact us at <a href="mailto:' . SUPPORT_EMAIL . '">' . SUPPORT_EMAIL . '</a></p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail($email, $subject, $body, true);
}

/**
 * Generate secure random token
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}
?>
