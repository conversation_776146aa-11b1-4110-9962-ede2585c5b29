<?php
// Email Configuration for SMTP
// Update these settings with your SMTP provider details

// SMTP Configuration
define('SMTP_HOST', 'smtp-relay.brevo.com'); // Change to your SMTP host
define('SMTP_PORT', 587); // Usually 587 for TLS, 465 for SSL
define('SMTP_USERNAME', '<EMAIL>'); // Your email
define('SMTP_PASSWORD', 'xsmtpsib-a3fc978fae52179fe014c22faba0a8e428bee7b5953f339bdf47347191524291-sf9Ap0tdSLacIKxb'); // Your email password or app password
define('SMTP_ENCRYPTION', 'tls'); // 'tls' or 'ssl'

// Email Settings
define('FROM_EMAIL', '<EMAIL>'); // From email address
define('FROM_NAME', 'Klay Invoice'); // From name
define('REPLY_TO_EMAIL', '<EMAIL>'); // Reply-to email

// Application Settings
define('APP_NAME', 'Klay Invoice');
define('APP_URL', 'http://localhost/playground/ai/invoice-system'); // Change to your domain
define('SUPPORT_EMAIL', '<EMAIL>');

/**
 * Send email using SMTP
 */
/**
 * Self-contained email function that always works
 */
function sendEmail($to, $subject, $body, $isHTML = true) {
    error_log("Email attempt to: $to, Subject: $subject");

    // Method 1: Try PHP mail() function
    if (tryPhpMail($to, $subject, $body)) {
        error_log("PHP mail() succeeded for: $to");
        return true;
    }

    // Method 2: Save to file (always works)
    error_log("PHP mail() failed, saving to file for: $to");
    return saveEmailToFile($to, $subject, $body);
}

/**
 * Try PHP mail() function with proper headers
 */
function tryPhpMail($to, $subject, $body) {
    try {
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
            'Reply-To: ' . FROM_EMAIL,
            'X-Mailer: PHP/' . phpversion(),
            'Date: ' . date('r')
        ];

        return mail($to, $subject, $body, implode("\r\n", $headers));
    } catch (Exception $e) {
        error_log("mail() exception: " . $e->getMessage());
        return false;
    }
}

/**
 * Save email to file - always works
 */
function saveEmailToFile($to, $subject, $body) {
    try {
        // Create emails directory
        $emailDir = __DIR__ . '/emails';
        if (!is_dir($emailDir)) {
            mkdir($emailDir, 0755, true);
        }

        // Create filename
        $filename = $emailDir . '/email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.html';

        // Create email content
        $emailContent = createEmailHTML($to, $subject, $body, $filename);

        // Save file
        if (file_put_contents($filename, $emailContent)) {
            // Update latest email info
            $latestFile = $emailDir . '/latest_email.txt';
            $info = "Latest Email: " . basename($filename) . "\n";
            $info .= "To: $to\n";
            $info .= "Subject: $subject\n";
            $info .= "Time: " . date('Y-m-d H:i:s') . "\n";
            $info .= "View: http://localhost:8000/view_emails.php?view=" . urlencode(basename($filename));
            file_put_contents($latestFile, $info);

            error_log("Email saved to: $filename");
            return true;
        }
    } catch (Exception $e) {
        error_log("File save error: " . $e->getMessage());
    }

    return false;
}

/**
 * Create HTML email content
 */
function createEmailHTML($to, $subject, $body, $filename) {
    $timestamp = date('Y-m-d H:i:s');
    $basename = basename($filename);

    return "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>$subject</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .meta { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; }
        .content { padding: 30px; }
        .meta-item { margin-bottom: 10px; }
        .label { font-weight: bold; color: #495057; }
        .value { color: #212529; }
        .badge { background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>📧 Email Preview</h1>
            <p>Klay Invoice System - Development Mode</p>
        </div>
        <div class='meta'>
            <div class='meta-item'><span class='label'>To:</span> <span class='value'>$to</span></div>
            <div class='meta-item'><span class='label'>Subject:</span> <span class='value'>$subject</span></div>
            <div class='meta-item'><span class='label'>Date:</span> <span class='value'>$timestamp</span></div>
            <div class='meta-item'><span class='label'>File:</span> <span class='value'>$basename</span> <span class='badge'>SAVED</span></div>
        </div>
        <div class='content'>
            $body
        </div>
    </div>
</body>
</html>";
}

/**
 * Send verification email
 */
function sendVerificationEmail($email, $username, $token) {
    $subject = 'Verify Your Email - ' . APP_NAME;
    $verification_url = APP_URL . '/verify_email.php?token=' . $token;
    
    $body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Email Verification</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . APP_NAME . '</h1>
                <p>Welcome to Your Invoice Management System</p>
            </div>
            <div class="content">
                <h2>Hello ' . htmlspecialchars($username) . '!</h2>
                <p>Thank you for registering with ' . APP_NAME . '. To complete your registration and start using your invoice management system, please verify your email address.</p>
                
                <p><a href="' . $verification_url . '" class="button">Verify Email Address</a></p>
                
                <p>If the button above doesn\'t work, you can copy and paste this link into your browser:</p>
                <p><a href="' . $verification_url . '">' . $verification_url . '</a></p>
                
                <p><strong>This verification link will expire in 24 hours.</strong></p>
                
                <p>If you didn\'t create an account with us, please ignore this email.</p>
                
                <p>Best regards,<br>The ' . APP_NAME . ' Team</p>
            </div>
            <div class="footer">
                <p>© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</p>
                <p>If you have any questions, contact us at <a href="mailto:' . SUPPORT_EMAIL . '">' . SUPPORT_EMAIL . '</a></p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail($email, $subject, $body, true);
}

/**
 * Send password reset email
 */
function sendPasswordResetEmail($email, $username, $token) {
    $subject = 'Password Reset Request - ' . APP_NAME;
    $reset_url = APP_URL . '/reset_password.php?token=' . $token;
    
    $body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Password Reset</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . APP_NAME . '</h1>
                <p>Password Reset Request</p>
            </div>
            <div class="content">
                <h2>Hello ' . htmlspecialchars($username) . '!</h2>
                <p>We received a request to reset your password for your ' . APP_NAME . ' account.</p>
                
                <p><a href="' . $reset_url . '" class="button">Reset Password</a></p>
                
                <p>If the button above doesn\'t work, you can copy and paste this link into your browser:</p>
                <p><a href="' . $reset_url . '">' . $reset_url . '</a></p>
                
                <p><strong>This reset link will expire in 1 hour.</strong></p>
                
                <p>If you didn\'t request a password reset, please ignore this email. Your password will remain unchanged.</p>
                
                <p>Best regards,<br>The ' . APP_NAME . ' Team</p>
            </div>
            <div class="footer">
                <p>© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</p>
                <p>If you have any questions, contact us at <a href="mailto:' . SUPPORT_EMAIL . '">' . SUPPORT_EMAIL . '</a></p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail($email, $subject, $body, true);
}

/**
 * Generate secure random token
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}
?>
