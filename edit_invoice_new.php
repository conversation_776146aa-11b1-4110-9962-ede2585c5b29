<?php
$page_title = "Klay Invoice - Edit Invoice ID: KL-INV-" . $_GET['id']; 
include 'includes/header.php'; 
include 'config.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    die("Invalid invoice ID.");
}

$invoice_id = $_GET['id'];

// Fetch Invoice Data
$stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = ?");
$stmt->execute([$invoice_id]);
$invoice = $stmt->fetch();

if (!$invoice) {
    die("Invoice not found.");
}

// Fetch Invoice Items
$stmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = ?");
$stmt->execute([$invoice_id]);
$invoice_items = $stmt->fetchAll();

// Fetch Clients and Items
$clients = $pdo->query("SELECT * FROM clients ORDER BY name ASC")->fetchAll();
$items = $pdo->query("SELECT * FROM items ORDER BY name ASC")->fetchAll();

// Handle Form Submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Process date fields - convert empty strings to NULL
    $invoice_date = !empty($_POST['invoice_date']) ? $_POST['invoice_date'] : null;
    $quote_date = !empty($_POST['quote_date']) ? $_POST['quote_date'] : null;
    $paid_date = !empty($_POST['paid_date']) ? $_POST['paid_date'] : null;
    
    // Update Invoice
    $stmt = $pdo->prepare("UPDATE invoices SET client_id = ?, invoice_for = ?, service_type = ?, invoice_date = ?, quote_date = ?, paid_date = ?, discount_rate = ?, discount_fixed = ?, discount_notes = ?, vat_rate = ?, tax_exempt = ?, payment_status = ? WHERE id = ?");
    $stmt->execute([
        $_POST['client_id'],
        $_POST['invoice_for'],
        $_POST['service_type'],
        $invoice_date,
        $quote_date,
        $paid_date,
        $_POST['discount_rate'],
        $_POST['discount_fixed'],
        $_POST['discount_notes'],
        $_POST['vat_rate'],
        $_POST['tax_exempt'],
        $_POST['payment_status'],
        $invoice_id
    ]);

    // Clear existing items
    $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = ?")->execute([$invoice_id]);

    // Insert Updated Items
    foreach ($_POST['item_id'] as $key => $item_id) {
        if (!empty($item_id)) {
            $stmt = $pdo->prepare("INSERT INTO invoice_items (invoice_id, item_id, special_notes, quantity, unit_price) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                $invoice_id,
                $item_id,
                $_POST['special_notes'][$key],
                $_POST['quantity'][$key],
                $_POST['price_override'][$key] ?: $pdo->query("SELECT price FROM items WHERE id = $item_id")->fetchColumn()
            ]);
        }
    }

    header("Location: view_invoice.php?id=$invoice_id");
    exit;
}
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-pencil-square"></i> Edit Invoice</h2>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="post" id="invoice-form">
                <!-- Client and Dates Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-person"></i> Client & Dates</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">Client</label>
                                <select name="client_id" class="form-select" required>
                                    <option value="">Select Client</option>
                                    <?php foreach($clients as $client): ?>
                                    <option value="<?= $client['id'] ?>" <?= ($client['id'] == $invoice['client_id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($client['name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Quotation Date</label>
                                <input type="date" name="quote_date" id="quote_date" class="form-control" value="<?= $invoice['quote_date'] ?>" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Invoice Date</label>
                                <input type="date" name="invoice_date" id="invoice_date" class="form-control" value="<?= $invoice['invoice_date'] ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Paid Date</label>
                                <input type="date" name="paid_date" id="paid_date" class="form-control" value="<?= $invoice['paid_date'] ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Details Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-info-circle"></i> Invoice Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-5">
                                <label class="form-label">Invoice Title</label>
                                <input type="text" name="invoice_for" class="form-control" value="<?= htmlspecialchars($invoice['invoice_for'] ?? '', ENT_QUOTES, 'UTF-8') ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Service Type</label>
                                <input type="text" name="service_type" placeholder="Add New Service Type" class="form-control" value="<?= htmlspecialchars($invoice['service_type'] ?? '', ENT_QUOTES, 'UTF-8') ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Or Existing Type</label>
                                <select id="service_type" class="form-select" name="service_type">
                                    <!-- options will be populated here -->
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing & Discounts Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-currency-dollar"></i> Pricing & Discounts</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">Discount (%)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="discount_rate" class="form-control" value="<?= $invoice['discount_rate'] ?>" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Discount (Fixed)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="discount_fixed" class="form-control" value="<?= $invoice['discount_fixed'] ?>" required>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">VAT Rate (%)</label>
                                <div class="input-group">
                                    <input type="number" step="0.50" name="vat_rate" class="form-control" value="<?= $invoice['vat_rate'] ?>" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">TAX Exempt?</label>
                                <select name="tax_exempt" class="form-select">
                                    <option value="yes" <?php if ($invoice['tax_exempt'] == 'yes') echo 'selected'; ?>>Yes</option>
                                    <option value="no" <?php if ($invoice['tax_exempt'] == 'no') echo 'selected'; ?>>No</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Payment</label>
                                <select name="payment_status" class="form-select">
                                    <option value="unpaid" <?php if ($invoice['payment_status'] == 'unpaid') echo 'selected'; ?>>Unpaid</option>
                                    <option value="processing" <?php if ($invoice['payment_status'] == 'processing') echo 'selected'; ?>>Processing</option>
                                    <option value="paid" <?php if ($invoice['payment_status'] == 'paid') echo 'selected'; ?>>Paid</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Discount Notes</label>
                                <input type="text" name="discount_notes" class="form-control" placeholder="Write discount notes" value="<?= htmlspecialchars($invoice['discount_notes'] ?? '', ENT_QUOTES, 'UTF-8') ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-check"></i> Invoice Items</h5>
                        <button type="button" class="btn btn-sm btn-primary" id="add-row">
                            <i class="bi bi-plus-circle"></i> Add Item
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="35%">Item</th>
                                        <th width="15%">Price Override</th>
                                        <th width="30%">Special Notes</th>
                                        <th width="10%">Quantity</th>
                                        <th width="10%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="items-container">
                                    <?php foreach ($invoice_items as $index => $item): ?>
                                    <tr class="item-row">
                                        <td>
                                            <select name="item_id[]" class="form-select item-select">
                                                <option value="">Select Item</option>
                                                <?php foreach ($items as $item_option): ?>
                                                <option value="<?= $item_option['id'] ?>" data-price="<?= $item_option['price'] ?>"
                                                    <?= ($item_option['id'] == $item['item_id']) ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($item_option['name']) ?> (<?= number_format($item_option['price'], 2) ?>)
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="number" name="price_override[]" class="form-control" placeholder="Override"
                                                value="<?= $item['unit_price'] ?>">
                                        </td>
                                        <td>
                                            <input type="text" name="special_notes[]" class="form-control" placeholder="Special Notes"
                                                value="<?= htmlspecialchars($item['special_notes'] ?? '', ENT_QUOTES, 'UTF-8') ?>">
                                        </td>
                                        <td>
                                            <input type="number" name="quantity[]" class="form-control quantity" placeholder="Qty" min="1"
                                                value="<?= $item['quantity'] ?>">
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger remove-row">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Invoice Summary Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-calculator"></i> Invoice Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td>Subtotal:</td>
                                        <td class="text-end" id="subtotal">0.00</td>
                                    </tr>
                                    <tr>
                                        <td id="discount-label">Discount (0.00%):</td>
                                        <td class="text-end" id="discount">-0.00</td>
                                    </tr>
                                    <tr>
                                        <td>Total:</td>
                                        <td class="text-end" id="after-discount">0.00</td>
                                    </tr>
                                    <tr>
                                        <td id="vat-label">VAT (5.00%):</td>
                                        <td class="text-end" id="vat">0.00</td>
                                    </tr>
                                    <tr class="table-active fw-bold">
                                        <td>Grand Total:</td>
                                        <td class="text-end" id="total-cell">0.00</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="view_invoice.php?id=<?= $invoice_id ?>" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="bi bi-check-circle"></i> Update Invoice
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const paymentStatus = document.querySelector('select[name="payment_status"]');
    const paidDateField = document.getElementById('paid-date-field');
    const paidDateInput = document.getElementById('paid_date');
    
    // Function to toggle paid date field visibility
    function updatePaidDateVisibility() {
        console.log('Payment status:', paymentStatus.value);
        
        if (paymentStatus.value === 'paid') {
            // Show paid date field
            paidDateField.style.display = '';
            
            // If no date is set, set to today
            if (!paidDateInput.value) {
                paidDateInput.value = new Date().toISOString().split('T')[0];
            }
            
            // Make it required
            paidDateInput.setAttribute('required', 'required');
        } else {
            // Hide paid date field
            paidDateField.style.display = 'none';
            
            // Make it not required
            paidDateInput.removeAttribute('required');
        }
    }
    
    // Run once on page load
    updatePaidDateVisibility();
    
    // Add event listener for changes
    paymentStatus.addEventListener('change', updatePaidDateVisibility);
});

document.getElementById('add-row').addEventListener('click', function() {
    const newRow = document.querySelector('.item-row').cloneNode(true);
    newRow.querySelectorAll('input').forEach(input => {
        if (input.classList.contains('quantity')) {
            input.value = '1';
        } else {
            input.value = '';
        }
    });
    newRow.querySelector('select').selectedIndex = 0;
    document.getElementById('items-container').appendChild(newRow);
    calculateTotals();
});

// Fix the remove row functionality
document.addEventListener('click', function(e) {
    // Check if the click is on the button or any of its children (like the icon)
    const removeButton = e.target.closest('.remove-row');
    if (removeButton) {
        if (document.querySelectorAll('.item-row').length > 1) {
            removeButton.closest('.item-row').remove();
            // Recalculate totals after removing a row
            calculateTotals();
        } else {
            alert('You cannot remove the last item row.');
        }
    }
});

// Calculate totals
function calculateTotals() {
    let subtotal = 0;
    
    document.querySelectorAll('.item-row').forEach(row => {
        const select = row.querySelector('.item-select');
        const quantity = parseInt(row.querySelector('.quantity').value) || 0;
        const priceOverride = parseFloat(row.querySelector('input[name="price_override[]"]').value) || 0;
        
        if (select.selectedIndex > 0) {
            const option = select.options[select.selectedIndex];
            const price = priceOverride > 0 ? priceOverride : parseFloat(option.dataset.price) || 0;
            subtotal += price * quantity;
        }
    });
    
    const discountRate = parseFloat(document.querySelector('input[name="discount_rate"]').value) || 0;
    const discountFixed = parseFloat(document.querySelector('input[name="discount_fixed"]').value) || 0;
    const vatRate = parseFloat(document.querySelector('input[name="vat_rate"]').value) || 0;
    const taxExempt = document.querySelector('select[name="tax_exempt"]').value;
    
    // Calculate discount amount
    let discountAmount = (subtotal * (discountRate / 100)) + discountFixed;
    const afterDiscount = subtotal - discountAmount;
    const vatAmount = afterDiscount * (vatRate / 100);
    const total = afterDiscount + vatAmount;
    
    // Format number with commas
    function formatNumber(num) {
        return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // Update discount label
    const discountLabel = document.getElementById('discount-label');
    if (discountLabel) {
        if (discountRate > 0 && discountFixed > 0) {
            discountLabel.textContent = `Discount (${discountRate.toFixed(2)}% + Fixed):`;
        } else if (discountRate > 0) {
            discountLabel.textContent = `Discount (${discountRate.toFixed(2)}%):`;
        } else if (discountFixed > 0) {
            discountLabel.textContent = 'Discount (Fixed):';
        } else {
            discountLabel.textContent = 'Discount:';
        }
    }
    
    // Update VAT label
    const vatLabel = document.getElementById('vat-label');
    if (vatLabel) {
        vatLabel.textContent = `VAT (${vatRate.toFixed(2)}%):`;
    }
    
    // Update all amounts with formatted numbers
    document.getElementById('subtotal').textContent = formatNumber(subtotal);
    document.getElementById('discount').textContent = '-' + formatNumber(discountAmount);
    document.getElementById('after-discount').textContent = formatNumber(afterDiscount);
    document.getElementById('vat').textContent = formatNumber(vatAmount);
    
    // Update total with tax exempt note if applicable
    const totalCell = document.getElementById('total-cell');
    if (totalCell) {
        if (taxExempt === 'yes') {
            totalCell.innerHTML = formatNumber(total) + '<br><small class="text-muted">(Tax Exempt)</small>';
        } else {
            totalCell.textContent = formatNumber(total);
        }
    }
}

// Add event listeners for calculation
document.querySelectorAll('input[name="discount_rate"], input[name="discount_fixed"], input[name="vat_rate"], select[name="tax_exempt"]')
    .forEach(el => el.addEventListener('input', calculateTotals));

document.getElementById('items-container').addEventListener('change', function(e) {
    if (e.target.classList.contains('item-select') || e.target.classList.contains('quantity') || 
        e.target.name === 'price_override[]') {
        calculateTotals();
    }
});

// Also listen for input events on quantity and price override
document.getElementById('items-container').addEventListener('input', function(e) {
    if (e.target.classList.contains('quantity') || e.target.name === 'price_override[]') {
        calculateTotals();
    }
});

// Calculate totals when the page loads
window.addEventListener('load', function() {
    calculateTotals();
    fetchServiceTypes();
});

function fetchServiceTypes() {
    var dropdown = document.getElementById('service_type');
    if (dropdown !== null) {
        var currentValue = dropdown.value;

        var xhr = new XMLHttpRequest();
        xhr.open('GET', 'fetch_service_types.php', true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                var serviceTypes = JSON.parse(xhr.responseText);
                dropdown.innerHTML = '<option value="">Please Select</option>';
                serviceTypes.forEach(function(serviceType) {
                    if (serviceType.service_type !== null && serviceType.service_type !== '') {
                        var option = document.createElement('option');
                        option.value = serviceType.service_type;
                        option.text = serviceType.service_type;
                        if (currentValue === serviceType.service_type) {
                            option.selected = true;
                        }
                        dropdown.appendChild(option);
                    }
                });
            }
        };
        xhr.send();
    }
}

window.addEventListener('load', function() {
    fetchServiceTypes();
});
</script>

<script>
    var inputField = document.querySelector('input[name="service_type"]');
    var selectField = document.querySelector('select[name="service_type"]');

    // Check initially if the input field is not empty
    if (inputField.value !== '') {
        selectField.disabled = true;
    }

    inputField.addEventListener('input', function() {
        if (inputField.value !== '') {
            selectField.disabled = true;
        } else {
            selectField.disabled = false;
        }
    });
</script>

<?php include 'includes/footer.php'; ?>
