<?php
// Email Debug Page - Detailed SMTP Testing
require_once 'smtp_mailer.php';

$message = '';
$message_type = '';
$debug_output = '';

if (isset($_POST['test_smtp'])) {
    $test_email = trim($_POST['test_email']);
    
    if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        $message = "Please enter a valid email address.";
        $message_type = "danger";
    } else {
        // Capture debug output
        ob_start();
        
        // Test SMTP connection
        $smtp = new SimpleSMTP(
            'smtp-relay.brevo.com',
            587,
            '<EMAIL>',
            '7Nt8Aw9qOhKjCmYs',
            'tls'
        );
        
        $smtp->setDebug(true);
        
        $subject = "SMTP Test from Klay Invoice System";
        $body = '<h2>SMTP Test Successful!</h2><p>Your Brevo SMTP configuration is working correctly.</p>';
        
        $result = $smtp->sendMail(
            $test_email,
            $subject,
            $body,
            '<EMAIL>',
            'Klay Invoice System'
        );
        
        $debug_output = ob_get_clean();
        
        if ($result) {
            $message = "SMTP test email sent successfully! Check your inbox.";
            $message_type = "success";
        } else {
            $message = "SMTP test failed. Check debug output below.";
            $message_type = "danger";
        }
    }
}

// Test basic connectivity
$connectivity_test = '';
if (isset($_POST['test_connection'])) {
    $host = 'smtp-relay.brevo.com';
    $port = 587;
    
    $connection = @fsockopen($host, $port, $errno, $errstr, 10);
    if ($connection) {
        $connectivity_test = "✅ Successfully connected to $host:$port";
        fclose($connection);
    } else {
        $connectivity_test = "❌ Failed to connect to $host:$port - Error: $errstr ($errno)";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Debug - Klay Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        body { background: #f8f9fa; font-family: 'Inter', sans-serif; }
        .debug-container { max-width: 800px; margin: 2rem auto; }
        .debug-output { background: #1e1e1e; color: #00ff00; font-family: 'Courier New', monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .config-table th { background: #e9ecef; }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0"><i class="bi bi-bug me-2"></i>Email Debug & Testing</h3>
            </div>
            <div class="card-body">
                
                <!-- Current Configuration -->
                <div class="mb-4">
                    <h5>Current Brevo SMTP Configuration:</h5>
                    <table class="table table-sm">
                        <tr><th>Host</th><td>smtp-relay.brevo.com</td></tr>
                        <tr><th>Port</th><td>587</td></tr>
                        <tr><th>Encryption</th><td>TLS</td></tr>
                        <tr><th>Username</th><td><EMAIL></td></tr>
                        <tr><th>Password</th><td>7Nt8Aw9qOhKjCmYs (first 10 chars)</td></tr>
                        <tr><th>From Email</th><td><EMAIL></td></tr>
                    </table>
                </div>
                
                <!-- Connectivity Test -->
                <div class="mb-4">
                    <h5>1. Test Basic Connectivity</h5>
                    <form method="post" class="d-inline">
                        <button type="submit" name="test_connection" class="btn btn-outline-primary">
                            <i class="bi bi-wifi me-1"></i>Test Connection
                        </button>
                    </form>
                    <?php if($connectivity_test): ?>
                        <div class="mt-2 p-2 bg-light rounded"><?= $connectivity_test ?></div>
                    <?php endif; ?>
                </div>
                
                <!-- SMTP Test -->
                <div class="mb-4">
                    <h5>2. Test SMTP Email Sending</h5>
                    <?php if(!empty($message)): ?>
                        <div class="alert alert-<?= $message_type ?> mb-3">
                            <i class="bi bi-<?= $message_type === 'success' ? 'check-circle' : 'exclamation-triangle' ?>-fill me-2"></i>
                            <?= $message ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post">
                        <div class="row">
                            <div class="col-md-8">
                                <input type="email" class="form-control" name="test_email" 
                                       placeholder="Enter test email address" required
                                       value="<?= isset($_POST['test_email']) ? htmlspecialchars($_POST['test_email']) : '' ?>">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" name="test_smtp" class="btn btn-primary w-100">
                                    <i class="bi bi-send me-1"></i>Send Test Email
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Debug Output -->
                <?php if(!empty($debug_output)): ?>
                <div class="mb-4">
                    <h5>3. Debug Output</h5>
                    <pre class="debug-output p-3 rounded"><?= htmlspecialchars($debug_output) ?></pre>
                </div>
                <?php endif; ?>
                
                <!-- PHP Configuration -->
                <div class="mb-4">
                    <h5>4. PHP Configuration</h5>
                    <table class="table table-sm">
                        <tr><th>PHP Version</th><td><?= phpversion() ?></td></tr>
                        <tr><th>OpenSSL</th><td><?= extension_loaded('openssl') ? '✅ Enabled' : '❌ Disabled' ?></td></tr>
                        <tr><th>Sockets</th><td><?= extension_loaded('sockets') ? '✅ Enabled' : '❌ Disabled' ?></td></tr>
                        <tr><th>Stream Sockets</th><td><?= function_exists('stream_socket_client') ? '✅ Available' : '❌ Not Available' ?></td></tr>
                        <tr><th>Mail Function</th><td><?= function_exists('mail') ? '✅ Available' : '❌ Not Available' ?></td></tr>
                    </table>
                </div>
                
                <!-- Troubleshooting Tips -->
                <div class="mb-4">
                    <h5>5. Troubleshooting Tips</h5>
                    <ul class="list-unstyled">
                        <li><strong>If connection fails:</strong> Check firewall settings, ensure port 587 is open</li>
                        <li><strong>If authentication fails:</strong> Verify Brevo SMTP credentials</li>
                        <li><strong>If TLS fails:</strong> Check OpenSSL extension and certificate store</li>
                        <li><strong>If all fails:</strong> Try port 465 with SSL instead of TLS</li>
                    </ul>
                </div>
                
                <div class="text-center">
                    <a href="test_email.php" class="btn btn-outline-secondary me-2">Simple Email Test</a>
                    <a href="register.php" class="btn btn-outline-primary me-2">Test Registration</a>
                    <a href="login.php" class="btn btn-outline-success">Back to Login</a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
