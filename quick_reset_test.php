<?php
// Quick password reset test - guaranteed to work
require_once 'config.php';
require_once 'email_config.php';

$step = isset($_GET['step']) ? $_GET['step'] : '1';
$message = '';

if ($step === '1') {
    // Step 1: Create or find a user and generate token
    echo "<h1>Step 1: Generate Reset Token</h1>";
    
    // Get or create test user
    $test_email = '<EMAIL>';
    $stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE email = ?");
    $stmt->execute([$test_email]);
    $user = $stmt->fetch();
    
    if (!$user) {
        // Create test user
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, company_name, email_verified) VALUES (?, ?, ?, ?, 1)");
        $stmt->execute(['testuser', $test_email, password_hash('oldpassword', PASSWORD_DEFAULT), 'Test Company']);
        $user_id = $pdo->lastInsertId();
        echo "<p>✅ Created test user: testuser ($test_email)</p>";
    } else {
        $user_id = $user['id'];
        echo "<p>✅ Using existing user: {$user['username']} ($test_email)</p>";
    }
    
    // Generate token
    $token = bin2hex(random_bytes(32));
    $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    // Store token
    $stmt = $pdo->prepare("INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)");
    $stmt->execute([$user_id, $token, $expires_at]);
    
    echo "<p>✅ Generated reset token</p>";
    echo "<p><strong>Token:</strong> $token</p>";
    echo "<p><strong>Expires:</strong> $expires_at</p>";
    
    $reset_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/quick_reset_test.php?step=2&token=" . $token;
    echo "<p><strong>Reset URL:</strong> <a href='$reset_url'>$reset_url</a></p>";
    
    echo "<hr>";
    echo "<h2>Test Links:</h2>";
    echo "<p><a href='$reset_url' class='btn btn-primary'>Test Reset Form</a></p>";
    echo "<p><a href='reset_password.php?token=$token&debug=1' class='btn btn-secondary'>Test Official Reset Page (Debug)</a></p>";
    
} elseif ($step === '2') {
    // Step 2: Show reset form
    $token = $_GET['token'] ?? '';
    
    if (empty($token)) {
        echo "<h1>❌ No Token Provided</h1>";
        echo "<p><a href='?step=1'>Go back to Step 1</a></p>";
        exit;
    }
    
    // Verify token
    $stmt = $pdo->prepare("SELECT prt.user_id, prt.expires_at, u.username, u.email FROM password_reset_tokens prt JOIN users u ON prt.user_id = u.id WHERE prt.token = ?");
    $stmt->execute([$token]);
    $token_data = $stmt->fetch();
    
    if (!$token_data) {
        echo "<h1>❌ Invalid Token</h1>";
        echo "<p>Token not found in database.</p>";
        echo "<p><a href='?step=1'>Generate new token</a></p>";
        exit;
    }
    
    if (strtotime($token_data['expires_at']) <= time()) {
        echo "<h1>❌ Expired Token</h1>";
        echo "<p>Token expired at: {$token_data['expires_at']}</p>";
        echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
        echo "<p><a href='?step=1'>Generate new token</a></p>";
        exit;
    }
    
    // Process form submission
    if (isset($_POST['reset_password'])) {
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        if (empty($new_password) || empty($confirm_password)) {
            $message = "All fields are required";
        } elseif ($new_password !== $confirm_password) {
            $message = "Passwords do not match";
        } elseif (strlen($new_password) < 6) {
            $message = "Password must be at least 6 characters long";
        } else {
            // Update password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            
            if ($stmt->execute([$hashed_password, $token_data['user_id']])) {
                // Delete token
                $stmt = $pdo->prepare("DELETE FROM password_reset_tokens WHERE token = ?");
                $stmt->execute([$token]);
                
                echo "<h1>✅ Password Reset Successful!</h1>";
                echo "<p>Password has been updated for user: {$token_data['username']}</p>";
                echo "<p>New password: $new_password</p>";
                echo "<p><a href='login.php'>Login with new password</a></p>";
                echo "<p><a href='?step=1'>Test again</a></p>";
                exit;
            } else {
                $message = "Failed to update password";
            }
        }
    }
    
    echo "<h1>Step 2: Reset Password</h1>";
    echo "<p><strong>User:</strong> {$token_data['username']} ({$token_data['email']})</p>";
    echo "<p><strong>Token expires:</strong> {$token_data['expires_at']}</p>";
    
    if ($message) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>$message</div>";
    }
    
    echo "<form method='post' style='max-width: 400px; margin: 20px 0;'>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label>New Password:</label><br>";
    echo "<input type='password' name='new_password' required style='width: 100%; padding: 8px;'>";
    echo "</div>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label>Confirm Password:</label><br>";
    echo "<input type='password' name='confirm_password' required style='width: 100%; padding: 8px;'>";
    echo "</div>";
    echo "<button type='submit' name='reset_password' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px;'>Reset Password</button>";
    echo "</form>";
    
    echo "<p><a href='?step=1'>Back to Step 1</a></p>";
}

echo "<hr>";
echo "<h2>Debug Info</h2>";
echo "<p><strong>Current Step:</strong> $step</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";

echo "<h2>Other Tools</h2>";
echo "<p><a href='generate_reset_link.php'>Generate Reset Link Tool</a></p>";
echo "<p><a href='debug_reset_tokens.php'>Debug All Tokens</a></p>";
echo "<p><a href='forgot_password.php'>Forgot Password Page</a></p>";
?>
