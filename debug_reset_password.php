<?php
// Debug version of reset_password.php to see exactly what's happening
require_once 'config.php';

$token = isset($_GET['token']) ? $_GET['token'] : '';

echo "<h1>Password Reset Token Debug</h1>";
echo "<p><strong>Token:</strong> " . htmlspecialchars($token) . "</p>";

if (empty($token)) {
    echo "<p>❌ No token provided</p>";
    exit;
}

echo "<h2>Step 1: Check if token exists in database (without expiry check)</h2>";
try {
    $stmt = $pdo->prepare("SELECT prt.*, u.username, u.email FROM password_reset_tokens prt LEFT JOIN users u ON prt.user_id = u.id WHERE prt.token = ?");
    $stmt->execute([$token]);
    $token_data = $stmt->fetch();
    
    if ($token_data) {
        echo "<p>✅ Token found in database</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($token_data as $key => $value) {
            if (!is_numeric($key)) {
                echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
            }
        }
        echo "</table>";
    } else {
        echo "<p>❌ Token not found in database</p>";
        echo "<p>This means the token was never created or has been deleted.</p>";
        
        // Show recent tokens for comparison
        echo "<h3>Recent tokens in database:</h3>";
        $stmt = $pdo->query("SELECT token, user_id, expires_at, created_at FROM password_reset_tokens ORDER BY created_at DESC LIMIT 5");
        $recent_tokens = $stmt->fetchAll();
        
        if ($recent_tokens) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Token (first 20 chars)</th><th>User ID</th><th>Expires</th><th>Created</th></tr>";
            foreach ($recent_tokens as $rt) {
                echo "<tr>";
                echo "<td>" . substr($rt['token'], 0, 20) . "...</td>";
                echo "<td>{$rt['user_id']}</td>";
                echo "<td>{$rt['expires_at']}</td>";
                echo "<td>{$rt['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No tokens found in database at all.</p>";
        }
        exit;
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>Step 2: Check expiry</h2>";
$current_time = date('Y-m-d H:i:s');
$expires_at = $token_data['expires_at'];

echo "<p><strong>Current time:</strong> $current_time</p>";
echo "<p><strong>Token expires:</strong> $expires_at</p>";

if (strtotime($expires_at) > time()) {
    echo "<p>✅ Token is not expired</p>";
    $is_valid = true;
} else {
    echo "<p>❌ Token is expired</p>";
    $time_diff = time() - strtotime($expires_at);
    echo "<p>Token expired " . round($time_diff / 60) . " minutes ago</p>";
    $is_valid = false;
}

echo "<h2>Step 3: Test the exact SQL query used in reset_password.php</h2>";
try {
    $stmt = $pdo->prepare("SELECT prt.user_id, prt.expires_at, u.username, u.email FROM password_reset_tokens prt JOIN users u ON prt.user_id = u.id WHERE prt.token = ? AND prt.expires_at > NOW()");
    $stmt->execute([$token]);
    $reset_data = $stmt->fetch();
    
    if ($reset_data) {
        echo "<p>✅ SQL query returns data - token should work</p>";
        echo "<p><strong>User:</strong> {$reset_data['username']} ({$reset_data['email']})</p>";
    } else {
        echo "<p>❌ SQL query returns no data - this is why reset fails</p>";
        
        // Test without expiry check
        echo "<h3>Testing without expiry check:</h3>";
        $stmt = $pdo->prepare("SELECT prt.user_id, prt.expires_at, u.username, u.email FROM password_reset_tokens prt JOIN users u ON prt.user_id = u.id WHERE prt.token = ?");
        $stmt->execute([$token]);
        $reset_data_no_expiry = $stmt->fetch();
        
        if ($reset_data_no_expiry) {
            echo "<p>✅ Token exists but is expired</p>";
        } else {
            echo "<p>❌ Token doesn't exist or user doesn't exist</p>";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ SQL error: " . $e->getMessage() . "</p>";
}

echo "<h2>Step 4: Generate a fresh token for testing</h2>";
if ($token_data && isset($token_data['user_id'])) {
    // Generate new token
    $new_token = bin2hex(random_bytes(32));
    $new_expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    try {
        $stmt = $pdo->prepare("INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = CURRENT_TIMESTAMP");
        $stmt->execute([$token_data['user_id'], $new_token, $new_expires]);
        
        $new_reset_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/reset_password.php?token=" . $new_token;
        
        echo "<p>✅ Generated fresh token</p>";
        echo "<p><strong>New token:</strong> $new_token</p>";
        echo "<p><strong>Expires:</strong> $new_expires</p>";
        echo "<p><strong>Test URL:</strong> <a href='$new_reset_url' target='_blank'>$new_reset_url</a></p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Failed to generate new token: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<h2>Summary</h2>";
if ($is_valid) {
    echo "<p>✅ The token should work. If it's still failing, there might be a code issue.</p>";
} else {
    echo "<p>❌ The token is expired. Use the fresh token generated above or request a new password reset.</p>";
}

echo "<hr>";
echo "<h2>Quick Actions</h2>";
echo "<p><a href='generate_reset_link.php'>Generate New Reset Link</a></p>";
echo "<p><a href='forgot_password.php'>Forgot Password Page</a></p>";
echo "<p><a href='debug_reset_tokens.php'>Debug All Tokens</a></p>";
?>
