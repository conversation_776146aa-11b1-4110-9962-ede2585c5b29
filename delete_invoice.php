<?php
session_start();
require_once 'auth.php';
include 'config.php';

if (isset($_POST['id'])) {
    $id = $_POST['id'];

    // First verify the invoice belongs to the current user
    $stmt = $pdo->prepare("SELECT id FROM invoices WHERE id = ? AND user_id = ?");
    $stmt->execute([$id, $_SESSION['user_id']]);

    if ($stmt->fetch()) {
        // Delete related records from other tables
        $stmt = $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
        $stmt->execute([$id]);

        // Delete the invoice
        $stmt = $pdo->prepare("DELETE FROM invoices WHERE id = ? AND user_id = ?");
        $stmt->execute([$id, $_SESSION['user_id']]);
    }

    header('Location: invoices.php');
    exit;
} else {
    header('Location: invoices.php');
    exit;
}
?>