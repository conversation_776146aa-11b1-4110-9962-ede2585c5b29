<?php
/**
 * Alternative email sending using cURL and Brevo API
 * This bypasses SMTP entirely and uses Brevo's REST API
 */

/**
 * Send email using Brevo API (alternative to SMTP)
 */
function sendEmailViaCurl($to, $subject, $body, $fromEmail = '<EMAIL>', $fromName = 'Klay Invoice System') {
    // Brevo API endpoint
    $url = 'https://api.brevo.com/v3/smtp/email';
    
    // Your Brevo API key (you'll need to get this from your Brevo account)
    // This is different from SMTP password - it's a REST API key
    $apiKey = 'YOUR_BREVO_API_KEY_HERE'; // Replace with your actual API key
    
    // Prepare email data
    $data = [
        'sender' => [
            'name' => $fromName,
            'email' => $fromEmail
        ],
        'to' => [
            [
                'email' => $to,
                'name' => $to
            ]
        ],
        'subject' => $subject,
        'htmlContent' => $body
    ];
    
    // Initialize cURL
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'api-key: ' . $apiKey
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false // For development only
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Log the attempt
    error_log("Brevo API attempt - HTTP Code: $httpCode, Response: $response");
    
    if ($error) {
        error_log("cURL error: $error");
        return false;
    }
    
    if ($httpCode === 201) {
        error_log("Email sent successfully via Brevo API");
        return true;
    } else {
        error_log("Brevo API error - HTTP $httpCode: $response");
        return false;
    }
}

/**
 * Test basic email sending using PHP mail() with proper headers
 */
function sendEmailBasic($to, $subject, $body, $fromEmail = '<EMAIL>', $fromName = 'Klay Invoice System') {
    // Enhanced headers for better delivery
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . $fromName . ' <' . $fromEmail . '>',
        'Reply-To: ' . $fromEmail,
        'Return-Path: ' . $fromEmail,
        'X-Mailer: PHP/' . phpversion(),
        'X-Priority: 3',
        'X-MSMail-Priority: Normal',
        'Message-ID: <' . uniqid() . '@' . $_SERVER['HTTP_HOST'] . '>',
        'Date: ' . date('r')
    ];
    
    $result = mail($to, $subject, $body, implode("\r\n", $headers));
    
    if ($result) {
        error_log("Basic mail() function succeeded");
    } else {
        error_log("Basic mail() function failed");
    }
    
    return $result;
}

/**
 * Ultimate fallback email function that tries everything
 */
function sendEmailUltimate($to, $subject, $body, $fromEmail = '<EMAIL>', $fromName = 'Klay Invoice System') {
    error_log("Starting ultimate email send to: $to");
    
    // Method 1: Try SMTP (if available)
    if (function_exists('sendEmailSMTP')) {
        error_log("Trying SMTP method");
        if (sendEmailSMTP($to, $subject, $body, $fromEmail, $fromName)) {
            error_log("SMTP method succeeded");
            return true;
        }
        error_log("SMTP method failed");
    }
    
    // Method 2: Try Brevo API (if API key is configured)
    if (defined('BREVO_API_KEY') && BREVO_API_KEY !== 'YOUR_BREVO_API_KEY_HERE') {
        error_log("Trying Brevo API method");
        if (sendEmailViaCurl($to, $subject, $body, $fromEmail, $fromName)) {
            error_log("Brevo API method succeeded");
            return true;
        }
        error_log("Brevo API method failed");
    }
    
    // Method 3: Try basic mail() function
    error_log("Trying basic mail() method");
    if (sendEmailBasic($to, $subject, $body, $fromEmail, $fromName)) {
        error_log("Basic mail() method succeeded");
        return true;
    }
    error_log("Basic mail() method failed");
    
    // Method 4: Save to file (always works)
    error_log("All email methods failed, saving to file");

    // Create emails directory if it doesn't exist
    $emailDir = __DIR__ . '/emails';
    if (!is_dir($emailDir)) {
        mkdir($emailDir, 0755, true);
    }

    // Save email to file
    $filename = $emailDir . '/email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.html';
    $emailContent = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>$subject</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .email-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .email-header { background: #007bff; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .email-meta { background: #e9ecef; padding: 10px; margin-bottom: 20px; border-radius: 4px; font-size: 14px; }
        .email-body { line-height: 1.6; }
    </style>
</head>
<body>
    <div class='email-container'>
        <div class='email-header'>
            <h2>📧 Email Preview - Development Mode</h2>
        </div>
        <div class='email-meta'>
            <strong>To:</strong> $to<br>
            <strong>Subject:</strong> $subject<br>
            <strong>Date:</strong> " . date('Y-m-d H:i:s') . "<br>
            <strong>File:</strong> " . basename($filename) . "
        </div>
        <div class='email-body'>
            $body
        </div>
    </div>
</body>
</html>";

    if (file_put_contents($filename, $emailContent)) {
        error_log("Email saved to file: $filename");

        // Create a notification file for easy access
        $notificationFile = $emailDir . '/latest_email.txt';
        file_put_contents($notificationFile, "Latest email saved: $filename\nTo: $to\nSubject: $subject\nTime: " . date('Y-m-d H:i:s'));

        return true; // Return true so registration doesn't fail
    }

    error_log("Failed to save email to file");
    return false;
}
?>
