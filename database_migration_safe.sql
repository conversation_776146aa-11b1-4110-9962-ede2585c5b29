-- Safe Multi-Tenant Invoice System Database Migration
-- This version handles existing constraints and columns safely

-- 1. Add email column to users table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'email') > 0,
    'SELECT "Email column already exists"',
    'ALTER TABLE users ADD COLUMN email VARCHAR(255) UNIQUE'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. Add other user columns if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'company_name') > 0,
    'SELECT "Company_name column already exists"',
    'ALTER TABLE users ADD COLUMN company_name VARCHAR(255)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'company_address') > 0,
    'SELECT "Company_address column already exists"',
    'ALTER TABLE users ADD COLUMN company_address TEXT'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'company_phone') > 0,
    'SELECT "Company_phone column already exists"',
    'ALTER TABLE users ADD COLUMN company_phone VARCHAR(50)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'company_email') > 0,
    'SELECT "Company_email column already exists"',
    'ALTER TABLE users ADD COLUMN company_email VARCHAR(255)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'company_website') > 0,
    'SELECT "Company_website column already exists"',
    'ALTER TABLE users ADD COLUMN company_website VARCHAR(255)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'email_verified') > 0,
    'SELECT "Email_verified column already exists"',
    'ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'verification_token') > 0,
    'SELECT "Verification_token column already exists"',
    'ALTER TABLE users ADD COLUMN verification_token VARCHAR(255)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'created_at') > 0,
    'SELECT "Created_at column already exists"',
    'ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'updated_at') > 0,
    'SELECT "Updated_at column already exists"',
    'ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. Add user_id columns to other tables
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'clients' 
     AND COLUMN_NAME = 'user_id') > 0,
    'SELECT "Clients user_id column already exists"',
    'ALTER TABLE clients ADD COLUMN user_id INT'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'items' 
     AND COLUMN_NAME = 'user_id') > 0,
    'SELECT "Items user_id column already exists"',
    'ALTER TABLE items ADD COLUMN user_id INT'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'invoices' 
     AND COLUMN_NAME = 'user_id') > 0,
    'SELECT "Invoices user_id column already exists"',
    'ALTER TABLE invoices ADD COLUMN user_id INT'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Update existing data to assign to user ID 1
UPDATE clients SET user_id = 1 WHERE user_id IS NULL;
UPDATE items SET user_id = 1 WHERE user_id IS NULL;
UPDATE invoices SET user_id = 1 WHERE user_id IS NULL;

-- 5. Create user settings table
CREATE TABLE IF NOT EXISTS user_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_setting (user_id, setting_key)
);

-- 6. Insert default settings for existing users
INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) 
SELECT id, 'invoice_prefix', 'INV-' FROM users WHERE id IS NOT NULL;

INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) 
SELECT id, 'default_vat_rate', '5.00' FROM users WHERE id IS NOT NULL;

INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) 
SELECT id, 'invoice_footer_text', 'Thank you for your business!' FROM users WHERE id IS NOT NULL;

INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) 
SELECT id, 'currency_symbol', 'BDT' FROM users WHERE id IS NOT NULL;

INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) 
SELECT id, 'date_format', 'd M Y' FROM users WHERE id IS NOT NULL;
