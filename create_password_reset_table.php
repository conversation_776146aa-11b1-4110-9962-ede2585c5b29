<?php
// Create password reset tokens table
require_once 'config.php';

try {
    // Create password_reset_tokens table
    $sql = "CREATE TABLE IF NOT EXISTS password_reset_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL UNIQUE,
        expires_at DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_token (token),
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at)
    )";
    
    $pdo->exec($sql);
    echo "✅ Password reset tokens table created successfully!\n";
    
    // Clean up expired tokens (optional cleanup)
    $cleanup_sql = "DELETE FROM password_reset_tokens WHERE expires_at < NOW()";
    $pdo->exec($cleanup_sql);
    echo "✅ Cleaned up expired tokens!\n";
    
} catch (PDOException $e) {
    echo "❌ Error creating table: " . $e->getMessage() . "\n";
}
?>
