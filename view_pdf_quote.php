<?php
//include 'includes/header.php'; 
include 'config.php';

if (isset($_GET['id'])) {
    $invoice_id = $_GET['id'];
    $invoice = $pdo->query("SELECT * FROM invoices 
                           JOIN clients ON invoices.client_id = clients.id
                           WHERE invoices.id = $invoice_id")->fetch();

    $items = $pdo->query("SELECT * FROM invoice_items 
                         JOIN items ON invoice_items.item_id = items.id
                         WHERE invoice_id = $invoice_id")->fetchAll();

    // Calculate Totals
    $subtotal = 0;
    foreach($items as $item){
        $subtotal += $item['unit_price'] * $item['quantity'];
    }
    $tax = $subtotal * ($invoice['vat_rate'] / 100);
    $total = $subtotal;

    $invoice_for = $invoice['invoice_for'];
    $service_type = $invoice['service_type'];
    $special_notes = $item['special_notes'];

    $invoice_date = date("d M Y", strtotime($invoice['invoice_date']));
    $quote_date = date("d M Y", strtotime($invoice['quote_date']));
    $invoice_id_date = date("ymd", strtotime($invoice['quote_date']));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo "Quotation: KL-QTN-" . $_GET['id']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            font-size: 12px;
        }
        .print-wrap {
            max-width: 1000px;
        }
        .invoice-header {
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 2rem;
        }
        .total-row {
            font-weight: bold;
        }
        .grand-total-row td,
        .table-light th {
            background-color: #f3f3f3;
        }
        .grand-total-row *,
        .table-light {
            font-size: 14px;
        }
        .grand-total-row em {
            font-weight: normal;
            font-size: 10px;
            display: block;
        }
        .total-row {
            font-size: 13px;
        }
        .signature-line {
            border-top: 1px solid #000;
            width: 200px;
            margin-top: 50px;
        }
        .tax-note {
            background-color:#eee;
            padding: 12px;
            border-radius: 4px;
            margin: 20px 0;
            font-size: 13px;
        }
        table.table * {
            border-color: #000;
        }
        h3 {
            font-size: 21px;
        }
        h4 {
            font-size: 17px;
        }
        h5 {
            font-size: 15px;
        }
        h6 {
            font-size: 12px;
        }
        .kl-payment,
        .kl-summary {
            font-size: 11px;
        }
        .kl-terms ol {
            padding: 0 0 0 13px;
            margin: 0;
            font-size: 11px;
        }
        .kl-item strong {
            font-size: 13px;
        }
        .kl-item p {
            font-size: 11px;
        }
        .kl-sm-font {
            font-size: 11px;
        }
        .kl-sm-font em {
            font-size: 10px;
        }
        footer {
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid print-wrap mt-2 mb-5">
        <header class="mb-5 text-end">
            <img src="assets/img/logo.svg" class="img-fluid" width="300">
        </header>
        <div class="card-wrap pt-3">
            <div class="card-body">
                <!-- Client Info -->
                <div class="row mb-4 pb-2">
                    <div class="col-8">
                        <h5 class="fw-bold mb-0">Client: <?= $invoice['name'] ?></h5>
                        <p class="mb-0"><strong>Address</strong>: <?= $invoice['address'] ?></p>
                    </div>
                    <div class="col-4 text-end">
                        <p class="mb-0"><strong>Quotation ID</strong>: KL-QTN-<?= $invoice_id_date ?>-<?= $invoice_id ?></p>
                        <p class="mb-0"><strong>Date</strong>: <?= $quote_date ?></p>
                    </div>
                </div>

                <!-- Title -->
                <div class="row mb-0">
                    <div class="col-12 text-center">
                        <h3 class="mb-0 fw-bold">Quotation</h3>
                        <h4 class="mb-0 fw-bold"><?= $invoice_for ?></h4>
                        <p>(<?= $service_type ?>)</p>
                    </div>
                </div>

                <!-- Bill Table -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center" width="5%">SL.</th>
                                <th width="30%">Services/Items</th>
                                <th width="30%">Description</th>
                                <th class="text-end" width="20%">Rate (BDT)</th>
                                <th width="15%" class="text-end">Price (BDT)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1; foreach($items as $item): ?>
                                <tr>
                                    <td class="text-center"><?= $i++ ?>.</td>
                                    <td class="kl-item">
                                        <strong><?= $item['name'] ?></strong>
                                        <p class="mb-0"><?= $item['special_notes'] ?></p>
                                    </td>
                                    <td class="kl-item kl-sm-font">
                                        <?= $item['description'] ?>
                                    </td>
                                    <td class="kl-item text-end kl-sm-font">
                                        <?= $item['summary'] ?> x <?= $item['quantity'] ?><br>
                                        <em class="kl-summary">(Rate @ BDT <?= number_format($item['unit_price'], 2) ?>)</em>
                                    </td>
                                    <td class="text-end"><?= number_format($item['unit_price'] * $item['quantity'], 2) ?></td>
                                </tr>
                            <?php endforeach; ?>

                            <tr class="total-row grand-total-row">
                                <td colspan="4" class="text-end">TOTAL</td>
                                <td class="text-end">
                                    <?= number_format($total, 2) ?>
                                    <em>(Excluding VAT)</em>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Payment Details -->
                <!-- <div class="row mt-4 kl-payment">
                    <div class="col-12">
                        <p class="mb-4">All payments made via Electronic Funds Transfer (EFT) or A/C Payee Cheque should be in favor of <strong>"Klay Technologies"</strong>.</p>
                    </div>
                </div> -->

                <!-- Terms -->
                <div class="row mt-4 kl-terms">
                    <div class="col-12">
                        <h6 class="fw-bold mb-1">N.B.</h6>
                        <ol class="mb-0">
                            <li><strong>Pricing Policy</strong>: Prices may change due to currency rates or service charges.</li>
                            <li><strong>Feature Additions</strong>: Additional features may increase cost and timeline.</li>
                            <li><strong>Project Timelines</strong>: Delays in providing documents or feedback may extend the timeline.</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="fixed-bottom text-center pt-3 mb-1">
        <p class="mb-0"><strong>Office</strong>: Flat# 1B, House# 460/A, Road# 6, Avenue# 7, Mirpur DOHS, Dhaka</p>
        <p class="mb-0"><strong>Phone</strong>: 01552454543  <strong>Email</strong>: <EMAIL>  <strong>Web</strong>: klay.tech</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
} else {
    echo "No invoice ID provided.";
    exit;
}
?>

<?php //include 'includes/footer.php'; ?>