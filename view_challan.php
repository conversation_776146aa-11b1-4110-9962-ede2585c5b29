<?php
//include 'includes/header.php'; 
include 'config.php';

if (isset($_GET['id'])) {
    $invoice_id = $_GET['id'];
    $invoice = $pdo->query("SELECT * FROM invoices 
                           JOIN clients ON invoices.client_id = clients.id
                           WHERE invoices.id = $invoice_id")->fetch();

    $items = $pdo->query("SELECT * FROM invoice_items 
                         JOIN items ON invoice_items.item_id = items.id
                         WHERE invoice_id = $invoice_id")->fetchAll();

    // Calculate Totals
    $subtotal = 0;
    foreach ($items as $item){
        $subtotal += $item['unit_price'] * $item['quantity'];
    }
    
    $discount_rate = $subtotal * ($invoice['discount_rate'] / 100);
    $discount_fixed = $invoice['discount_fixed'];
    $total = $subtotal - ($discount_rate + $discount_fixed);
    $tax = $total * ($invoice['vat_rate'] / 100);
    $total_grand = $total + $tax;

    $invoice_for = $invoice['invoice_for'];
    $service_type = $invoice['service_type'];
    $special_notes = $item['special_notes'];

    $invoice_date = date("d/m/Y", strtotime($invoice['invoice_date']));
    //$invoice_id_date = date("ymd", strtotime($invoice['invoice_date']));
    //$due_date = date("d M Y", strtotime($invoice['due_date']));
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo "Challan: " . $_GET['id']; ?></title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;700&display=swap">

        <style>
            body {
                font-family: 'Noto Sans Bengali', sans-serif;
                font-size: 12px;
            }

            .print-wrap {
                max-width: 1000px;
            }

            .invoice-header {
                border-bottom: 2px solid #dee2e6;
                margin-bottom: 2rem;
            }

            .total-row {
                font-weight: bold;
            }

            .grand-total-row td,
            .table-light th {
                background-color: #f3f3f3;
            }

            .grand-total-row *,
            .table-light {
                font-size: 14px;
            }

            .grand-total-row em {
                font-weight: normal;
                font-size: 10px;
                display: block;
            }

            .total-row {
                font-size: 13px;
            }

            .signature-line {
                border-top: 1px solid #000;
                width: 200px;
                margin-top: 50px;
            }

            .tax-note {
                background-color: #eee;
                padding: 12px;
                border-radius: 4px;
                margin: 20px 0;
                font-size: 13px;
            }

            table.table * {
                border-color: #000;
            }

            h3 {
                font-size: 21px;
            }

            h4 {
                font-size: 17px;
            }

            h5 {
                font-size: 15px;
            }

            h6 {
                font-size: 12px;
            }

            .kl-payment,
            .kl-summary {
                font-size: 11px;
            }

            .kl-terms ol {
                padding: 0 0 0 13px;
                margin: 0;
                font-size: 11px;
            }

            .kl-item strong {
                font-size: 13px;
            }

            .kl-item p {
                font-size: 11px;
            }

            .kl-sm-font {
                font-size: 11px;
            }

            .kl-sm-font em {
                font-size: 10px;
            }

            footer {
                font-size: 10px;
            }

            .ch-head .text-end p {
                font-weight: bold;
            }
            .ch-head .text-end p+p {
                font-size: 15px;
            }
            .ch-head .text-center,
            .ch-head .text-center h3 {
                font-size: 12px;
            }
            .ch-head .text-center h2 {
                font-weight: bold;
                font-size: 19px;
            }

            .ch-by *,
            .ch-to *,
            .ch-sign * {
                border: 0px;
                vertical-align: middle;
            }
            .ch-sign *,
            .ch-content tbody * {
                vertical-align: top;
            }
            .ch-by {
                max-width: 550px;
                margin: 0 auto 20px auto;
            }
            .ch-by td,
            .ch-to td,
            .ch-sign td {
                padding: 0;
            }
            .ch-to td {
                padding-bottom: 1px;
                padding-top: 1px;
            }
            .ch-sign td {
                padding-top: 15px;
                padding-bottom: 50px;
                line-height: 1.8em;
            }
            .ch-to {
                position: relative;
            }
            .ch-no {
                font-size: 21px;
                font-weight: bold;
                display: inline-block;
                position: absolute;
                top: -4px;
            }
            .ch-by tr td:first-child{
                padding-left: 150px;
            }
            .ch-content * {
                font-size: 9px;
                vertical-align: middle;
            }
            .ch-content tr th {
                padding-bottom: 2px;
                padding-top: 2px;
                font-weight: 500;
            }
            .ch-content tbody tr td:last-child {
                padding-bottom: 250px;
            }
            .ch-content tfoot * {
                font-size: 11px;
            }
        </style>
    </head>

    <body>
        <div class="container-fluid print-wrap mt-2 mb-5">

                <div class="row ch-head">
                    <div class="col-3"></div>
                    <div class="col-6 text-center">
                        <h3>গণপ্রজাতন্ত্রী বাংলাদেশ সরকার, জাতীয় রাজস্ব বোর্ড, ঢাকা</h3>
                        <h2 class="mb-1">কর চালানপত্র</h2>
                        <p>[বিধি ৪০-এর উপ-বিধি (১)-এর দফা (গ) ও (চ) দ্রষ্টব্য]</p>
                    </div>
                    <div class="col-3 text-end">
                        <p>প্রথম কপি</p>
                        <p>মূসক - ৬.৩</p>
                    </div>
                </div>




                <table class="table ch-by">
                    <thead>
                        <tr>
                            <td><strong>নিবন্ধিত ব্যক্তির নাম:</strong></td>
                            <td>Klay Technologies</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>নিবন্ধিত ব্যক্তির বিআইএন:</strong></td>
                            <td>002039755-0101</td>
                        </tr>
                        <tr>
                            <td><strong>চালানপত্র ইস্যুর ঠিকানা:</strong></td>
                            <td>H#460/A, R#6, Ave#7, Mirpur DOHS, Dhaka</td>
                        </tr>
                    </tbody>
                </table>

                <table class="table table-bordered ch-to">
                    <thead>
                        <tr>
                            <td><strong>ক্রেতার নাম:</strong></td>
                            <td><?= $invoice['name'] ?></td>
                            <td><strong>চালানপত্র নম্বর:</strong></td>
                            <td><span class="ch-no"><?= $invoice_id ?></span></td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>ক্রেতার বিআইএন:</strong></td>
                            <td><?= $invoice['bin'] ?></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td><strong>ক্রেতার ঠিকানা:</strong></td>
                            <td><?= $invoice['address'] ?></td>
                            <td><strong>ইস্যুর তারিখ:</strong></td>
                            <td><?= $invoice_date ?></td>
                        </tr>
                        <tr>
                            <td><strong>সরবরাহের গন্তব্যস্থল:</strong></td>
                            <td>Dhaka</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td><strong>যানবাহনের প্রকৃতি ও নম্বর:</strong></td>
                            <td>Online</td>
                            <td><strong>ইস্যুর সময়:</strong></td>
                            <td>11:00am</td>
                        </tr>
                    </tbody>
                </table>

                <table class="table table-bordered ch-content">
                    <thead>
                        <tr class="text-center">
                            <th>ক্রমিক</th>
                            <th width="15%">পণ্য বা সেবার বর্ণনা (প্রযোজ্য ক্ষেত্রে ব্র্যান্ড নামসহ)</th>
                            <th>সরবরাহের একক</th>
                            <th>পরিমাণ</th>
                            <th>একক মূল্য<sup>১</sup> (টাকায়)</th>
                            <th>মোট মূল্য (টাকায়)</th>
                            <th>সম্পূরক শুল্কের হার</th>
                            <th>সম্পূরক শুল্কের পরিমাণ (টাকায়)</th>
                            <th>মূল্য সংযোজন করের হার/ সুনির্দিষ্ট কর</th>
                            <th>মূল্য সংযোজন করের পরিমাণ (টাকায়)</th>
                            <th>সকল প্রকার শুল্ক ও করসহ মূল্য</th>
                        </tr>
                        <tr class="text-center">
                            <th>(১)</th>
                            <th>(২)</th>
                            <th>(৩)</th>
                            <th>(৪)</th>
                            <th>(৫)</th>
                            <th>(৬)</th>
                            <th>(৭)</th>
                            <th>(৮)</th>
                            <th>(৯)</th>
                            <th>(১০)</th>
                            <th>(১১)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="text-center">
                            <td>1</td>
                            <td class="text-start"><?= $service_type ?></td>
                            <td>-</td>
                            <td>1</td>
                            <td><?= number_format($total, 2) ?>/-</td>
                            <td><?= number_format($total, 2) ?>/-</td>
                            <td>-</td>
                            <td>-</td>
                            <td><?= $invoice['vat_rate'] ?>%</td>
                            <td><?= number_format($tax, 2) ?>/-</td>
                            <td><?= number_format($total_grand, 2) ?>/-</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr class="text-center">
                            <td colspan="5" class="text-end"><strong>সর্বমোট</strong></td>
                            <td><strong><?= number_format($total, 2) ?>/-</strong></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><strong><?= number_format($tax, 2) ?>/-</strong></td>
                            <td><strong><?= number_format($total_grand, 2) ?>/-</strong></td>
                        </tr>
                    </tfoot>
                </table>

                <table class="table table-bordered ch-sign">
                    <tbody>
                        <tr>
                            <td>
                                <div><strong>প্রতিষ্ঠান কর্তৃপক্ষের দায়িত্বপ্রাপ্ত ব্যক্তির নাম:</strong> Nusrat Mahmud</div>
                                <div><strong>পদবী:</strong> Accounts Officer</div>
                                <div><strong>স্বাক্ষর:</strong></div>
                            </td>
                            <td width="20%"><strong>সীল:</strong></td>
                        </tr>
                    </tbody>
                </table>

                <p style="font-size:10px;"><strong><sup>১</sup>সকল প্রকার কর ব্যতীত মূল্য</strong></p>

        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>

    </html>

<?php
} else {
    echo "No invoice ID provided.";
    exit;
}
?>

<?php //include 'includes/footer.php'; 
?>