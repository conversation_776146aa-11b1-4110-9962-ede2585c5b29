<?php
//include 'includes/header.php'; 
include 'config.php';

if (isset($_GET['id'])) {
    $invoice_id = $_GET['id'];
    $invoice = $pdo->query("SELECT * FROM invoices 
                           JOIN clients ON invoices.client_id = clients.id
                           WHERE invoices.id = $invoice_id")->fetch();

    $items = $pdo->query("SELECT * FROM invoice_items 
                         JOIN items ON invoice_items.item_id = items.id
                         WHERE invoice_id = $invoice_id")->fetchAll();

    // Calculate Totals
    $subtotal = 0;
    foreach ($items as $item){
        $subtotal += $item['unit_price'] * $item['quantity'];
    }
    
    $discount_rate = $subtotal * ($invoice['discount_rate'] / 100);
    $discount_fixed = $invoice['discount_fixed'];
    $total = $subtotal - ($discount_rate + $discount_fixed);
    $tax = $total * ($invoice['vat_rate'] / 100);
    $total_grand = $total + $tax;

    $invoice_for = $invoice['invoice_for'];
    $service_type = $invoice['service_type'];
    //$special_notes = $item['special_notes'];

    $invoice_date = date("d M Y", strtotime($invoice['invoice_date']));
    $quote_date = date("d M Y", strtotime($invoice['quote_date']));
    $invoice_id_date = date("ymd", strtotime($invoice['quote_date']));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo "Invoice: KL-INV-" . $_GET['id']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            font-size: 12px;
        }
        .print-wrap {
            max-width: 1000px;
        }
        .invoice-header {
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 2rem;
        }
        .total-row {
            font-weight: bold;
        }
        .grand-total-row td,
        .table-light th {
            background-color: #f3f3f3;
        }
        .grand-total-row *,
        .table-light {
            font-size: 14px;
        }
        .grand-total-row em {
            font-weight: normal;
            font-size: 10px;
            display: block;
        }
        .total-row {
            font-size: 13px;
        }
        .signature-line {
            border-top: 1px solid #000;
            width: 200px;
            margin-top: 50px;
        }
        .tax-note {
            background-color:#eee;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0 10px 0;
            font-size: 13px;
        }
        table.table * {
            border-color: #000;
        }
        h4 {
            font-size: 17px;
        }
        h5 {
            font-size: 15px;
        }
        h6 {
            font-size: 12px;
        }
        .kl-payment,
        .kl-summary {
            font-size: 11px;
        }
        .kl-terms ol {
            padding: 0 0 0 13px;
            margin: 0;
            font-size: 11px;
        }
        .kl-item strong {
            font-size: 13px;
        }
        .kl-item p {
            font-size: 11px;
        }
        footer {
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid print-wrap mt-2 mb-5">
        <header class="mb-5 text-end">
            <img src="assets/img/logo.svg" class="img-fluid" width="300">
        </header>
        <div class="card-wrap pt-3">
            <div class="card-body">
                <!-- Client Info -->
                <div class="row mb-4 pb-2">
                    <div class="col-8">
                        <h5 class="fw-bold mb-0">Invoice to: <?= $invoice['name'] ?></h5>
                        <p class="mb-0"><strong>Address</strong>: <?= $invoice['address'] ?></p>
                    </div>
                    <div class="col-4 text-end">
                        <p class="mb-0"><strong>Invoice ID</strong>: KL-INV-<?= $invoice_id_date ?>-<?= $invoice_id ?></p>
                        <p class="mb-0"><strong>Date</strong>: <?= $invoice_date ?></p>
                    </div>
                </div>

                <!-- Title -->
                <div class="row mb-0">
                    <div class="col-12 text-center">
                        <h4 class="mb-0 fw-bold">Invoice for <?= $invoice_for ?></h4>
                        <p>(<?= $service_type ?>)</p>
                    </div>
                </div>

                <!-- Bill Table -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center" width="5%">SL.</th>
                                <th width="55%">Services/Items</th>
                                <th class="text-end" width="25%">Rate (BDT)</th>
                                <th width="15%" class="text-end">Price (BDT)</th>
                            </tr>
                        </thead>
                        <tbody>
                            
                            <?php $i = 1; foreach($items as $item): ?>
                                <tr>
                                    <td class="text-center"><?= $i++ ?>.</td>
                                    <td class="kl-item">
                                        <strong><?= $item['name'] ?></strong>
                                        <p class="mb-0"><?= $item['special_notes'] ?></p>
                                    </td>
                                    <td class="kl-item text-end">
                                        <em class="kl-summary"><?= $item['summary'] ?> x <?= $item['quantity'] ?><br>
                                        (Rate @ BDT <?= number_format($item['unit_price'], 2) ?>)</em>
                                    </td>
                                    <td class="text-end"><?= number_format($item['unit_price'] * $item['quantity'], 2) ?></td>
                                </tr>
                            <?php endforeach; ?>

                            <?php /* if ($invoice['vat_rate'] > 0): ?>
                                <tr class="total-row">
                                    <td colspan="3" class="text-end">TOTAL</td>
                                    <td class="text-end"><?= number_format($subtotal, 2) ?></td>
                                </tr>
                                <tr class="total-row">
                                    <td colspan="3" class="text-end">VAT (<?= $invoice['vat_rate'] ?>%)</td>
                                    <td class="text-end"><?= number_format($tax, 2) ?></td>
                                </tr>
                            <?php endif; */ ?>

                            <tr class="text-end total-row">
                                <th colspan="3">SUBTOTAL</th>
                                <td><?= number_format($subtotal, 2) ?></td>
                            </tr>

                            <?php if ($invoice['discount_rate'] > 0 || $invoice['discount_fixed'] > 0): ?>
                                <tr class="text-end total-row">
                                    <td colspan="3">DISCOUNT
                                        <?php if ($invoice['discount_rate'] > 0): ?>
                                            (<?= $invoice['discount_rate'] ?>%)
                                        <?php else: ?>
                                            (Onetime)
                                        <?php endif; ?>
                                        <div style="font-weight: normal; font-size: 13px; font-style: italic;"><?= $invoice['discount_notes'] ?></div>
                                    </td>
                                    <td>
                                        <?php if ($invoice['discount_rate'] > 0): ?>
                                            -<?= number_format($discount_rate, 2) ?>
                                        <?php else: ?>
                                            -<?= number_format($invoice['discount_fixed'], 2) ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php if ($invoice['vat_rate'] > 0): ?>
                                <tr class="text-end total-row">
                                    <td colspan="3">TOTAL</td>
                                    <td><?= number_format($total, 2) ?></td>
                                </tr>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if ($invoice['vat_rate'] > 0): ?>
                            <tr class="text-end total-row">
                                <td colspan="3">VAT (<?= $invoice['vat_rate'] ?>%)</td>
                                <td><?= number_format($tax, 2) ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr class="text-end total-row grand-total-row">
                                <td colspan="3">GRAND TOTAL</td>
                                <td>
                                    <?= number_format($total_grand, 2) ?>
                                    <?php if ($invoice['vat_rate'] == 0): ?>
                                        <em>(Excluding VAT)</em>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <?php if ($invoice['tax_exempt'] == 'yes'): ?>
                <!-- Tax Note -->
                <div class="tax-note text-center">
                    <p class="mb-0 fw-bold">TAX-exempted IT-enabled services: (Tax Exemption Certificate Attached).</p>
                </div>
                <?php endif; ?>

                <!-- Payment Details -->
                <div class="row mt-4 kl-payment">
                    <div class="col-7">
                        <p class="mb-0">All payments made via Electronic Funds Transfer (EFT) or A/C Payee Cheque should be in favor of <strong>"Klay Technologies"</strong>.</p>
                        <p class="mb-0">Please find below the bank account details for EFT:</p>
                        <p class="mb-0"><strong>Account Name:</strong> Klay Technologies</p>
                        <p class="mb-0"><strong>Account Number:</strong> ***********</p>
                        <p class="mb-0"><strong>Bank Name:</strong> Bank Asia PLC</p>
                        <p class="mb-0"><strong>Branch:</strong> MCB Banani (Rounting No: *********)</p>
                    </div>
                    <div class="col-5 kl-terms">
                        <h6 class="fw-bold mb-1">Payment Terms</h6>
                        <ol class="mb-0">
                            <li>The bill must be settled within 10 days from the date of presentation.</li>
                            <li>Any objections must be raised within 7 days from the date of receipt; otherwise, they will not be entertained.</li>
                        </ol>
                    </div>
                </div>

                <!-- Terms -->
                <!-- <div class="row mt-4 kl-terms">
                    <div class="col-12">
                        <h6 class="fw-bold mb-1">Payment Terms</h6>
                        <ol class="mb-0">
                            <li>The bill must be settled within 10 days from the date of presentation.</li>
                            <li>Any objections must be raised within 7 days from the date of receipt; otherwise, they will not be entertained.</li>
                        </ol>
                    </div>
                </div> -->

                <!-- Signature -->
                <div class="row mt-5">
                    <div class="col-6">
                        <div class="signature-line"></div>
                        <p class="mb-0 mt-1 fw-bold">Nasim Ahmed</p>
                        <p class="mb-0">CEO, Klay Technologies</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="fixed-bottom text-center pt-3 mb-1">
        <p class="mb-0"><strong>Office</strong>: Flat# 1B, House# 460/A, Road# 6, Avenue# 7, Mirpur DOHS, Dhaka</p>
        <p class="mb-0"><strong>Phone</strong>: 01552454543  <strong>Email</strong>: <EMAIL>  <strong>Web</strong>: klay.tech</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
} else {
    echo "No invoice ID provided.";
    exit;
}
?>

<?php //include 'includes/footer.php'; ?>