</div>

<!-- Simple Professional Footer -->
<footer class="bg-light border-top mt-5">
    <div class="container">
        <div class="row py-4">
            <div class="col-md-6">
                <div class="d-flex align-items-center mb-2">
                    <i class="bi bi-receipt-cutoff text-primary me-2 fs-5"></i>
                    <span class="fw-semibold text-dark">Klay Invoice</span>
                </div>
                <p class="text-muted small mb-0">
                    Professional invoice management system for your business.
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="text-muted small mb-2">
                    © 2025 <strong class="text-dark">Klay Technologies</strong>. All rights reserved.
                </p>
                <div class="d-flex justify-content-md-end gap-3">
                    <a href="settings.php" class="text-muted text-decoration-none small">Settings</a>
                    <a href="backup.php" class="text-muted text-decoration-none small">Backup</a>
                    <span class="text-muted small">Version 1.0</span>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

<!-- Custom JavaScript -->
<script>
// Add loading states to submit buttons only
document.addEventListener('DOMContentLoaded', function() {
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Only apply loading state to submit buttons
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="loading me-2"></span>Processing...';
            this.disabled = true;

            // Re-enable after 3 seconds (adjust as needed)
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 3000);
        });
    });
});
</script>

</body>
</html>